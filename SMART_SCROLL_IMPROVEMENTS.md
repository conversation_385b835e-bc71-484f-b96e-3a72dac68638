# 智能滾動功能改進

## 概述
本次更新針對眼動控制的滾動功能進行了重大改進，特別是增強了向下滾動的體驗，讓使用者眼睛看下方時能有更大的滾動範圍，並且讓下方文字智能地移動到眼睛居中位置。

## 主要改進

### 1. 智能滾動控制器 (SmartScrollController)
- **新增檔案**: `app/src/main/java/com/oojohn/vision/domain/usecase/SmartScrollController.kt`
- **功能**: 
  - 計算智能滾動目標位置
  - 支援不同靈敏度的滾動距離調整
  - 實現平滑滾動動畫
  - 邊界檢查防止過度滾動

### 2. 增強的滾動設定
- **更新檔案**: `app/src/main/java/com/oojohn/vision/domain/model/ArticleContent.kt`
- **新增設定**:
  - `upScrollDistance`: 向上滾動距離 (預設: 100px)
  - `downScrollDistance`: 向下滾動距離 (預設: 200px，增加了一倍)
  - `smartScrollEnabled`: 智能滾動開關 (預設: true)

### 3. 智能向下滾動邏輯
當使用者眼睛看向下方時：
- **傳統模式**: 固定滾動距離
- **智能模式**: 計算螢幕中央位置，讓下方文字移動到眼睛容易看到的中央區域
- **計算公式**: 
  ```kotlin
  val screenCenter = screenHeight / 2
  val bottomViewArea = screenHeight * 0.75f  // 下方 75% 位置
  val scrollDistance = (bottomViewArea - screenCenter).toInt()
  ```

### 4. 無障礙服務增強
- **更新檔案**: `app/src/main/java/com/oojohn/vision/service/EyeControlAccessibilityService.kt`
- **新增方法**: `performScrollDownEnhanced()`
- **功能**: 
  - 支援多次滾動以達到更大滾動距離
  - 增強的手勢滾動，滑動距離更長、速度更平滑
  - 從螢幕 30% 位置滑動到 90% 位置

### 5. 文章閱讀畫面更新
- **更新檔案**: `app/src/main/java/com/oojohn/vision/ui/screen/ArticleDetailScreen.kt`
- **改進**:
  - 整合智能滾動控制器
  - 動態計算螢幕高度
  - 詳細的滾動日誌記錄
  - 更精確的滾動控制

## 技術細節

### 滾動距離對比
| 滾動方向 | 原始距離 | 新距離 | 改進幅度 |
|---------|---------|--------|---------|
| 向上滾動 | 100px | 100px | 無變化 |
| 向下滾動 | 100px | 200-250px | **增加 100-150%** |

### 智能滾動算法
```kotlin
private fun calculateSmartDownScroll(
    scrollState: ScrollState,
    screenHeight: Int,
    settings: ReadingSettings
): Int {
    val screenCenter = screenHeight / 2
    val bottomViewArea = screenHeight * 0.75f
    val scrollDistance = (bottomViewArea - screenCenter).toInt()
    val adjustedDistance = (scrollDistance * (settings.downScrollDistance / 200f)).toInt()
    return (scrollState.value + adjustedDistance).coerceAtMost(scrollState.maxValue)
}
```

### 靈敏度調整
- **低靈敏度**: 滾動距離 × 0.7
- **中等靈敏度**: 滾動距離 × 1.0 (預設)
- **高靈敏度**: 滾動距離 × 1.3

## 測試覆蓋
- **新增測試檔案**: `app/src/test/java/com/oojohn/vision/domain/usecase/SmartScrollControllerTest.kt`
- **測試案例**:
  - 向上/向下滾動計算
  - 智能滾動開關測試
  - 邊界限制測試
  - 靈敏度調整測試
  - 滾動能力檢查

## 使用者體驗改進

### 之前的問題
1. 向下滾動距離太小，需要多次眼動才能看到更多內容
2. 滾動後文字位置不理想，使用者需要調整視線
3. 滾動體驗不夠流暢

### 改進後的體驗
1. ✅ **向下滾動範圍增加一倍**，一次眼動可以看到更多內容
2. ✅ **智能定位**，下方文字自動移動到眼睛容易看到的中央位置
3. ✅ **平滑動畫**，滾動過程更加流暢自然
4. ✅ **邊界保護**，防止滾動超出內容範圍

## 配置選項
使用者可以透過 `ReadingSettings` 自訂滾動行為：
```kotlin
val customSettings = ReadingSettings(
    upScrollDistance = 80,        // 自訂向上滾動距離
    downScrollDistance = 300,     // 自訂向下滾動距離
    smartScrollEnabled = true,    // 啟用智能滾動
    sensitivity = SensitivityLevel.HIGH  // 高靈敏度
)
```

## 向後相容性
- 所有現有功能保持不變
- 新功能預設啟用，但可以透過設定關閉
- 不影響現有的眼動追蹤邏輯

## 未來擴展
1. 支援橫向滾動
2. 自適應滾動距離（根據內容密度調整）
3. 使用者自訂滾動曲線
4. 眼動停留時間影響滾動距離
