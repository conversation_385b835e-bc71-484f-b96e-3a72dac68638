package com.oojohn.vision.data.repository

import com.oojohn.vision.domain.model.ArticleContent
import com.oojohn.vision.domain.repository.ArticleRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 文章資料存取實作
 * 目前提供本地模擬資料，未來可擴展至資料庫或 API
 */
@Singleton
class ArticleRepositoryImpl @Inject constructor() : ArticleRepository {
    
    private val mockArticles = listOf(
        ArticleContent(
            id = "1",
            title = "眼動追蹤技術與應用",
            author = "科技研究員",
            content = """
                眼動追蹤技術是一種能夠記錄和分析人眼運動的技術。透過追蹤眼睛的移動，我們可以了解人們如何閱讀、瀏覽網頁或與介面互動。
                
                這項技術在多個領域都有重要應用：
                
                1. 可用性研究：了解使用者如何與產品互動
                2. 醫療診斷：輔助診斷某些神經系統疾病
                3. 教育研究：分析學習者的注意力模式
                4. 市場研究：評估廣告或產品包裝的效果
                
                隨著技術的發展，眼動追蹤設備變得更加精確和易於使用，為各種應用場景提供了新的可能性。
                
                眼動追蹤的工作原理主要基於瞳孔和角膜反射的檢測。現代眼動追蹤系統通常使用紅外線攝影機來捕捉眼部影像，然後使用計算機視覺算法來識別瞳孔位置和眼球運動。
                
                在移動裝置上實現眼動追蹤面臨著特殊的挑戰，包括光線變化、設備移動和計算資源限制。然而，隨著機器學習技術的進步，這些挑戰正在逐步得到解決。
            """.trimIndent(),
            datePublished = "2025-06-10",
            estimatedReadingTime = 4
        ),
        ArticleContent(
            id = "2",
            title = "人工智慧在現代生活中的應用",
            author = "AI 專家",
            content = """
                人工智慧 (AI) 已經深入我們的日常生活，從智慧手機助手到自動駕駛汽車，AI 技術正在改變我們與世界互動的方式。
                
                AI 的主要應用領域包括：
                
                1. 智慧助手：語音識別和自然語言處理
                2. 影像識別：物體檢測和臉部識別
                3. 推薦系統：個性化內容推薦
                4. 自動化：流程自動化和決策支援
                
                未來，AI 將繼續發展，為人類帶來更多便利和可能性。
                
                機器學習是 AI 的核心技術之一，它讓電腦能夠從資料中學習和改進，而不需要明確的程式設計指令。深度學習作為機器學習的一個分支，已經在圖像識別、語音處理和自然語言理解等領域取得了突破性進展。
                
                在醫療領域，AI 正在協助醫生進行疾病診斷、藥物發現和個性化治療。在教育領域，AI 可以提供個性化的學習體驗和智慧化的評估系統。
            """.trimIndent(),
            datePublished = "2025-06-08",
            estimatedReadingTime = 3
        ),
        ArticleContent(
            id = "3",
            title = "移動應用程式開發趨勢",
            author = "開發者",
            content = """
                移動應用程式開發領域不斷演進，新的技術和趨勢持續出現。
                
                當前的主要趨勢包括：
                
                1. 跨平台開發：React Native、Flutter 等框架的興起
                2. 5G 技術：更快的網路速度帶來新的應用可能性
                3. AR/VR 整合：增強現實和虛擬現實的應用
                4. AI 整合：機器學習和人工智慧功能的普及
                5. 物聯網 (IoT)：與智慧設備的連接
                
                開發者需要持續學習新技術，以保持競爭力。
                
                Jetpack Compose 作為 Android 的現代 UI 工具包，正在改變 Android 開發的方式。它採用聲明式 UI 方法，讓開發者能夠更輕鬆地建構美觀且高效的使用者介面。
                
                在安全性方面，移動應用程式面臨著越來越多的威脅，包括資料洩露、惡意軟體和隱私侵犯。開發者需要採用最佳的安全實踐，包括加密、安全的身份驗證和定期的安全審計。
                
                雲端整合也成為現代移動應用的標準功能，允許使用者在多個設備間同步資料和提供即時協作功能。
            """.trimIndent(),
            datePublished = "2025-06-05",
            estimatedReadingTime = 5
        ),
        ArticleContent(
            id = "4",
            title = "使用者體驗設計原則",
            author = "UX 設計師",
            content = """
                使用者體驗（UX）設計是創造有意義且相關的產品體驗的過程。好的 UX 設計不僅讓產品看起來美觀，更重要的是讓使用者能夠輕鬆、高效地完成他們的目標。
                
                核心設計原則包括：
                
                1. 以使用者為中心：了解使用者的需求、目標和痛點
                2. 一致性：在整個產品中保持設計和互動的一致性
                3. 可用性：確保產品易於學習和使用
                4. 可及性：讓所有使用者都能使用產品
                5. 回饋機制：為使用者的操作提供清晰的回饋
                
                在移動應用設計中，還需要考慮觸控操作、螢幕尺寸限制和使用情境的多樣性。
                
                設計流程通常包括研究、設計、原型製作和測試等階段。使用者研究幫助我們了解目標使用者，而原型製作和使用者測試則讓我們能夠在開發前驗證設計假設。
                
                隨著技術的發展，UX 設計也在不斷演進，包括語音介面、手勢控制和人工智慧輔助設計等新興領域。
            """.trimIndent(),
            datePublished = "2025-06-03",
            estimatedReadingTime = 4
        ),
        ArticleContent(
            id = "5",
            title = "資料科學與大數據分析",
            author = "資料科學家",
            content = """
                資料科學結合了統計學、電腦科學和領域專業知識，從資料中提取有價值的洞察。在大數據時代，組織能夠收集和儲存前所未有的資料量，但如何有效分析和利用這些資料成為關鍵挑戰。
                
                資料科學的主要流程包括：
                
                1. 資料收集：從各種來源獲取相關資料
                2. 資料清理：處理缺失值、異常值和不一致性
                3. 探索性資料分析：了解資料的分布和模式
                4. 模型建立：使用統計和機器學習方法
                5. 結果解釋：將技術結果轉化為業務洞察
                
                常用的工具包括 Python、R、SQL 和各種視覺化工具。
                
                在實際應用中，資料科學被廣泛用於商業智慧、風險管理、產品推薦、欺詐檢測和市場分析等領域。成功的資料科學專案不僅需要技術技能，還需要良好的溝通能力和業務理解。
                
                隨著機器學習和人工智慧技術的發展，資料科學家的角色也在演變，需要更多地關注模型的可解釋性、倫理考量和自動化流程。
            """.trimIndent(),
            datePublished = "2025-06-01",
            estimatedReadingTime = 6
        )
    )
    
    private val cachedArticleIds = mutableSetOf<String>()
    
    override fun getArticleList(): Flow<List<ArticleContent>> = flowOf(mockArticles)
    
    override fun getArticleById(articleId: String): Flow<ArticleContent?> {
        val article = mockArticles.find { it.id == articleId }
        return flowOf(article)
    }
    
    override fun searchArticles(query: String): Flow<List<ArticleContent>> = flow {
        val filteredArticles = mockArticles.filter { article ->
            article.title.contains(query, ignoreCase = true) ||
            article.content.contains(query, ignoreCase = true) ||
            article.author.contains(query, ignoreCase = true)
        }
        emit(filteredArticles)
    }
    
    override suspend fun cacheArticle(articleId: String) {
        cachedArticleIds.add(articleId)
    }
    
    override fun getCachedArticles(): Flow<List<ArticleContent>> = flow {
        val cachedArticles = mockArticles.filter { article ->
            cachedArticleIds.contains(article.id)
        }
        emit(cachedArticles)
    }
}