package com.oojohn.vision.data.repository

import android.content.Context
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.lifecycle.LifecycleOwner
import com.oojohn.vision.domain.model.EyeGazePosition
import com.oojohn.vision.domain.model.UIState
import com.oojohn.vision.domain.repository.CameraRepository
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetectorOptions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.util.concurrent.Executors
import javax.inject.Inject
import javax.inject.Singleton

/**
 * CameraRepository 實作，封裝 CameraX 與 ML Kit 臉部偵測。
 * @property context Application Context，由 DI 注入。
 */
@Singleton
class CameraRepositoryImpl @Inject constructor(
    private val context: Context
) : CameraRepository {
    private val cameraExecutor = Executors.newSingleThreadExecutor()
    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    private val _eyeGazePosition = MutableStateFlow<EyeGazePosition>(EyeGazePosition.Unknown)
    override val eyeGazePosition: StateFlow<EyeGazePosition> = _eyeGazePosition.asStateFlow()

    private val _cameraState = MutableStateFlow<UIState<Unit>>(UIState.Idle)
    override val cameraState: StateFlow<UIState<Unit>> = _cameraState.asStateFlow()

    private var cameraProvider: ProcessCameraProvider? = null
    private var imageAnalysis: ImageAnalysis? = null
    private var sensitivity: Float = 0.5f

    private val faceDetectorOptions = FaceDetectorOptions.Builder()
        .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST)
        .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
        .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
        .setContourMode(FaceDetectorOptions.CONTOUR_MODE_ALL)
        .build()
    private val faceDetector = FaceDetection.getClient(faceDetectorOptions)

    /**
     * 啟動相機與影像分析。
     * @return 是否成功啟動
     */
    override suspend fun startCamera(lifecycleOwner: LifecycleOwner): Boolean {
        return try {
            android.util.Log.d("EyeTracking", "開始啟動相機，當前狀態: ${_cameraState.value}")
            _cameraState.value = UIState.Loading

            val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
            cameraProvider = cameraProviderFuture.get()
            cameraProvider?.unbindAll()

            android.util.Log.d("EyeTracking", "相機提供者已獲取，開始配置")

            val preview = Preview.Builder().build()
            imageAnalysis = ImageAnalysis.Builder()
                .setTargetResolution(android.util.Size(640, 480))
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .build().also { analysis ->
                    analysis.setAnalyzer(cameraExecutor) @androidx.camera.core.ExperimentalGetImage { imageProxy ->
                        processImage(imageProxy)
                    }
                }

            val cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA
            cameraProvider?.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageAnalysis
            )

            _cameraState.value = UIState.Success(Unit)
            android.util.Log.d("EyeTracking", "相機啟動成功")
            true
        } catch (e: Exception) {
            android.util.Log.e("EyeTracking", "相機啟動失敗: ${e.message}", e)
            _cameraState.value = UIState.Error(e.localizedMessage ?: "相機初始化失敗")
            false
        }
    }

    /**
     * 停止相機。
     */
    override fun stopCamera() {
        cameraProvider?.unbindAll()
        _cameraState.value = UIState.Idle
        _eyeGazePosition.value = EyeGazePosition.Unknown
    }

    /**
     * 設定眼動追蹤靈敏度。
     * @param s 靈敏度(0.0~1.0)
     */
    override fun setEyeTrackingSensitivity(s: Float) {
        sensitivity = s
    }

    /**
     * 影像分析與臉部/眼睛座標判斷。
     * @param imageProxy CameraX 影像
     */
    @androidx.camera.core.ExperimentalGetImage
    private fun processImage(imageProxy: ImageProxy) {
        val mediaImage = imageProxy.image ?: run {
            imageProxy.close(); return
        }
        val inputImage = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)
        coroutineScope.launch {
            try {
                val faces = faceDetector.process(inputImage).await()
                if (faces.isEmpty()) {
                    android.util.Log.d("EyeTracking", "沒有偵測到臉部")
                    val oldPosition = _eyeGazePosition.value
                    _eyeGazePosition.value = EyeGazePosition.Unknown
                    if (oldPosition != EyeGazePosition.Unknown) {
                        android.util.Log.d("EyeTracking", "位置更新: $oldPosition -> Unknown (無臉部)")
                    }
                } else {
                    val face = faces[0]
                    val position = calculateEyeGazePosition(face, mediaImage.height, mediaImage.width)
                    val oldPosition = _eyeGazePosition.value
                    
                    // 只有在位置真的改變時才更新和記錄
                    if (oldPosition != position) {
                        _eyeGazePosition.value = position
                        android.util.Log.d("EyeTracking", "位置更新: $oldPosition -> $position (時間: ${System.currentTimeMillis()})")
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("EyeTracking", "處理影像時發生錯誤: ${e.message}")
                _eyeGazePosition.value = EyeGazePosition.Unknown
            } finally {
                imageProxy.close()
            }
        }
    }

    /**
     * 計算眼動位置的詳細邏輯
     * @param face 偵測到的臉部
     * @param imageHeight 影像高度
     * @param imageWidth 影像寬度
     * @return 眼動位置
     */
    private fun calculateEyeGazePosition(
        face: com.google.mlkit.vision.face.Face,
        imageHeight: Int,
        imageWidth: Int
    ): EyeGazePosition {
        val leftEye = face.getLandmark(com.google.mlkit.vision.face.FaceLandmark.LEFT_EYE)
        val rightEye = face.getLandmark(com.google.mlkit.vision.face.FaceLandmark.RIGHT_EYE)
        val noseBase = face.getLandmark(com.google.mlkit.vision.face.FaceLandmark.NOSE_BASE)
        
        // 確保所有必要的特徵點都存在
        if (leftEye == null || rightEye == null || noseBase == null) {
            android.util.Log.d("EyeTracking", "缺少必要的特徵點")
            return EyeGazePosition.Unknown
        }
        
        // 計算眼睛中心點
        val eyeCenterY = (leftEye.position.y + rightEye.position.y) / 2
        val noseY = noseBase.position.y
        
        // 計算眼睛與鼻子的相對位置
        val eyeToNoseDistance = noseY - eyeCenterY
        
        // 使用頭部傾斜角度作為主要判斷依據
        val headEulerAngleX = face.headEulerAngleX // 頭部上下傾斜角度
        
        // 根據靈敏度調整角度閾值
        // 靈敏度越高，閾值越小，越容易觸發
        val baseSensitivity = 8.0f // 基礎角度閾值
        val adjustedSensitivity = baseSensitivity * (1.0f - sensitivity * 0.7f) // 靈敏度調整
        
        // 詳細調試日誌
        android.util.Log.d("EyeTracking", "眼動計算詳情:")
        android.util.Log.d("EyeTracking", "  - 頭部角度: $headEulerAngleX")
        android.util.Log.d("EyeTracking", "  - 角度閾值: $adjustedSensitivity")
        android.util.Log.d("EyeTracking", "  - 眼鼻距離: $eyeToNoseDistance")
        android.util.Log.d("EyeTracking", "  - 影像高度: $imageHeight")
        android.util.Log.d("EyeTracking", "  - 靈敏度: $sensitivity")

        // 同時考慮頭部角度和眼鼻相對位置
        val result = when {
            // 向上看：頭部向上傾斜超過閾值
            headEulerAngleX > adjustedSensitivity -> {
                android.util.Log.d("EyeTracking", "判斷為向上看 (頭部角度 $headEulerAngleX > $adjustedSensitivity)")
                EyeGazePosition.Top
            }
            // 向下看：頭部向下傾斜超過閾值
            headEulerAngleX < -adjustedSensitivity -> {
                android.util.Log.d("EyeTracking", "判斷為向下看 (頭部角度 $headEulerAngleX < -$adjustedSensitivity)")
                EyeGazePosition.Bottom
            }
            // 輔助判斷：如果角度不明顯，使用眼鼻距離
            eyeToNoseDistance < imageHeight * 0.08f -> {
                android.util.Log.d("EyeTracking", "判斷為向上看 (眼鼻距離 $eyeToNoseDistance < ${imageHeight * 0.08f})")
                EyeGazePosition.Top
            }
            eyeToNoseDistance > imageHeight * 0.12f -> {
                android.util.Log.d("EyeTracking", "判斷為向下看 (眼鼻距離 $eyeToNoseDistance > ${imageHeight * 0.12f})")
                EyeGazePosition.Bottom
            }
            // 中央位置
            else -> {
                android.util.Log.d("EyeTracking", "判斷為中央位置")
                EyeGazePosition.Center
            }
        }

        android.util.Log.d("EyeTracking", "最終計算結果: $result")
        return result
    }
}
