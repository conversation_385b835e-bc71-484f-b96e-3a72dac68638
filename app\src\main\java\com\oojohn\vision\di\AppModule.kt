package com.oojohn.vision.di

import com.oojohn.vision.data.repository.ArticleRepositoryImpl
import com.oojohn.vision.data.repository.CameraRepositoryImpl
import com.oojohn.vision.domain.repository.ArticleRepository
import com.oojohn.vision.domain.repository.CameraRepository
import com.oojohn.vision.domain.usecase.SmartScrollController
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 應用程式層級的依賴注入模組
 * 提供 Repository 的實作綁定
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class AppModule {

    /**
     * 綁定 CameraRepository 介面與實作
     * @param cameraRepositoryImpl Repository 實作類別
     * @return CameraRepository 介面
     */
    @Binds
    @Singleton
    abstract fun bindCameraRepository(
        cameraRepositoryImpl: CameraRepositoryImpl
    ): CameraRepository
    
    /**
     * 綁定 ArticleRepository 介面與實作
     * @param articleRepositoryImpl Repository 實作類別
     * @return ArticleRepository 介面
     */
    @Binds
    @Singleton
    abstract fun bindArticleRepository(
        articleRepositoryImpl: ArticleRepositoryImpl
    ): ArticleRepository
}

/**
 * 提供具體實例的模組
 */
@Module
@InstallIn(SingletonComponent::class)
object AppProvidesModule {

    /**
     * 提供 SmartScrollController 實例
     * @return SmartScrollController 實例
     */
    @Provides
    @Singleton
    fun provideSmartScrollController(): SmartScrollController {
        return SmartScrollController()
    }
}