package com.oojohn.vision.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 提供基本依賴的 DI 模組
 * 包含 Context 等基礎依賴項
 */
@Module
@InstallIn(SingletonComponent::class)
object ContextModule {

    /**
     * 提供應用程式 Context
     * @param context Application Context
     * @return Context 實例
     */
    @Provides
    @Singleton
    fun provideContext(@ApplicationContext context: Context): Context = context
} 