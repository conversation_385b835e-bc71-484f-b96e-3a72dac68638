package com.oojohn.vision.domain.model

/**
 * 文章內容資料類別
 */
data class ArticleContent(
    val id: String = "",
    val title: String,
    val author: String,
    val content: String,
    val datePublished: String,
    val estimatedReadingTime: Int
)

/**
 * 閱讀設定資料類別
 */
data class ReadingSettings(
    val autoScrollEnabled: Boolean = true,
    val sensitivity: SensitivityLevel = SensitivityLevel.MEDIUM,
    val scrollSpeed: Int = 5,
    val upScrollDistance: Int = 100,      // 向上滾動距離
    val downScrollDistance: Int = 200,    // 向下滾動距離（增加範圍）
    val smartScrollEnabled: Boolean = true // 智能滾動：讓下方文字移到中央
)

/**
 * 靈敏度等級列舉
 */
enum class SensitivityLevel {
    LOW, MEDIUM, HIGH
}
