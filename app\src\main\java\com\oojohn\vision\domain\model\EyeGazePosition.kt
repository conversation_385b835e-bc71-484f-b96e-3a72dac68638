package com.oojohn.vision.domain.model

/**
 * 眼睛注視位置密封類別
 */
sealed class EyeGazePosition {
    object Top : EyeGazePosition()
    object Center : EyeGazePosition()
    object Bottom : EyeGazePosition()
    object Unknown : EyeGazePosition()
}

/**
 * 滾動方向列舉
 */
enum class ScrollDirection {
    UP, DOWN, NONE
}

/**
 * 滾動配置資料類別
 */
data class ScrollConfig(
    val upDistance: Int = 100,
    val downDistance: Int = 200,
    val animationDuration: Long = 300,
    val smartScrollEnabled: Boolean = true,
    val preventOverScroll: Boolean = true
)
