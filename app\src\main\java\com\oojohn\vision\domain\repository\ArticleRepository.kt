package com.oojohn.vision.domain.repository

import com.oojohn.vision.domain.model.ArticleContent
import kotlinx.coroutines.flow.Flow

/**
 * 文章資料存取介面
 * 定義文章相關的資料操作方法
 */
interface ArticleRepository {
    
    /**
     * 取得所有文章列表
     * @return 文章列表的 Flow
     */
    fun getArticleList(): Flow<List<ArticleContent>>
    
    /**
     * 根據 ID 取得特定文章
     * @param articleId 文章 ID
     * @return 文章內容的 Flow，如果找不到則為 null
     */
    fun getArticleById(articleId: String): Flow<ArticleContent?>
    
    /**
     * 搜尋文章
     * @param query 搜尋關鍵字
     * @return 符合搜尋條件的文章列表
     */
    fun searchArticles(query: String): Flow<List<ArticleContent>>
    
    /**
     * 快取文章供離線閱讀
     * @param articleId 要快取的文章 ID
     */
    suspend fun cacheArticle(articleId: String)
    
    /**
     * 取得快取的文章列表
     * @return 已快取的文章列表
     */
    fun getCachedArticles(): Flow<List<ArticleContent>>
}