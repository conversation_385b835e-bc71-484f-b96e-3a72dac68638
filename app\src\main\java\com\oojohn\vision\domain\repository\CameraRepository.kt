package com.oojohn.vision.domain.repository

import androidx.lifecycle.LifecycleOwner
import com.oojohn.vision.domain.model.EyeGazePosition
import com.oojohn.vision.domain.model.UIState
import kotlinx.coroutines.flow.StateFlow

/**
 * Camera 資料來源介面，負責相機與眼動偵測功能。
 */
interface CameraRepository {
    /** 眼睛注視位置流 */
    val eyeGazePosition: StateFlow<EyeGazePosition>
    /** 相機狀態流 */
    val cameraState: StateFlow<UIState<Unit>>
    /** 啟動相機與分析 */
    suspend fun startCamera(lifecycleOwner: LifecycleOwner): Boolean
    /** 停止相機 */
    fun stopCamera()
    /** 設定靈敏度 */
    fun setEyeTrackingSensitivity(s: Float)
}
