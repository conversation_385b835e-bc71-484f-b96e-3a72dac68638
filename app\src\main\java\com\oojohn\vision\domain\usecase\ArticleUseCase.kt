package com.oojohn.vision.domain.usecase

import com.oojohn.vision.domain.model.ArticleContent
import com.oojohn.vision.domain.repository.ArticleRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import javax.inject.Inject

/**
 * 文章內容使用案例，處理文章內容相關邏輯
 */
class ArticleUseCase @Inject constructor(
    private val articleRepository: ArticleRepository
) {
    private val _currentArticle = MutableStateFlow<ArticleContent?>(null)
    val currentArticle: StateFlow<ArticleContent?> = _currentArticle.asStateFlow()

    /**
     * 取得文章列表
     * @return 文章列表的 Flow
     */
    fun getArticleList(): Flow<List<ArticleContent>> {
        return articleRepository.getArticleList()
    }
    
    /**
     * 根據 ID 取得特定文章
     * @param articleId 文章 ID
     * @return 文章內容的 Flow
     */
    fun getArticleById(articleId: String): Flow<ArticleContent?> {
        return articleRepository.getArticleById(articleId)
    }
    
    /**
     * 搜尋文章
     * @param query 搜尋關鍵字
     * @return 符合搜尋條件的文章列表
     */
    fun searchArticles(query: String): Flow<List<ArticleContent>> {
        return articleRepository.searchArticles(query)
    }
    
    /**
     * 快取文章供離線閱讀
     * @param articleId 要快取的文章 ID
     */
    suspend fun cacheArticleForOfflineReading(articleId: String) {
        articleRepository.cacheArticle(articleId)
    }
    
    /**
     * 取得快取的文章列表
     * @return 已快取的文章列表
     */
    fun getCachedArticles(): Flow<List<ArticleContent>> {
        return articleRepository.getCachedArticles()
    }

    /**
     * 載入並設定當前文章
     * @param articleId 要載入的文章 ID
     */
    suspend fun loadArticle(articleId: String) {
        val article = articleRepository.getArticleById(articleId).first()
        _currentArticle.value = article
    }
}
