package com.oojohn.vision.domain.usecase

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import com.oojohn.vision.service.AccessibilityController
import com.oojohn.vision.service.EyeTrackingService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 背景服務管理使用案例
 * 處理背景眼動追蹤服務的啟動、停止和權限管理
 */
@Singleton
class BackgroundServiceUseCase @Inject constructor(
    private val context: Context
) {
    
    private val _isServiceRunning = MutableStateFlow(false)
    val isServiceRunning: StateFlow<Boolean> = _isServiceRunning.asStateFlow()
    
    private val _overlayPermissionGranted = MutableStateFlow(false)
    val overlayPermissionGranted: StateFlow<Boolean> = _overlayPermissionGranted.asStateFlow()
    
    private val _accessibilityServiceEnabled = MutableStateFlow(false)
    val accessibilityServiceEnabled: StateFlow<Boolean> = _accessibilityServiceEnabled.asStateFlow()
    
    private val accessibilityController = AccessibilityController(context)
    
    /**
     * 啟動背景眼動追蹤服務
     */
    fun startBackgroundService() {
        EyeTrackingService.startService(context)
        _isServiceRunning.value = true
    }
    
    /**
     * 停止背景眼動追蹤服務
     */
    fun stopBackgroundService() {
        EyeTrackingService.stopService(context)
        _isServiceRunning.value = false
    }
    
    /**
     * 檢查覆蓋權限是否已授予
     */
    fun checkOverlayPermission(): Boolean {
        val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
        _overlayPermissionGranted.value = hasPermission
        return hasPermission
    }
    
    /**
     * 請求覆蓋權限
     */
    fun requestOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(
                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                Uri.parse("package:${context.packageName}")
            ).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        }
    }
    
    /**
     * 檢查無障礙服務是否已啟用
     */
    fun checkAccessibilityService(): Boolean {
        val isEnabled = accessibilityController.isAccessibilityServiceEnabled()
        _accessibilityServiceEnabled.value = isEnabled
        return isEnabled
    }
    
    /**
     * 開啟無障礙服務設定
     */
    fun openAccessibilitySettings() {
        accessibilityController.openAccessibilitySettings()
    }
    
    /**
     * 檢查所有必要權限
     */
    fun checkAllPermissions(): Boolean {
        val overlayOk = checkOverlayPermission()
        val accessibilityOk = checkAccessibilityService()
        return overlayOk && accessibilityOk
    }
    
    /**
     * 更新服務運行狀態
     */
    fun updateServiceRunningState(isRunning: Boolean) {
        _isServiceRunning.value = isRunning
    }
    
    /**
     * 更新覆蓋權限狀態
     */
    fun updateOverlayPermissionState(granted: Boolean) {
        _overlayPermissionGranted.value = granted
    }
    
    /**
     * 更新無障礙服務狀態
     */
    fun updateAccessibilityServiceState(enabled: Boolean) {
        _accessibilityServiceEnabled.value = enabled
    }
}