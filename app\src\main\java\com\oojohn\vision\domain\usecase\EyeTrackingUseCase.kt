package com.oojohn.vision.domain.usecase

import androidx.lifecycle.LifecycleOwner
import com.oojohn.vision.domain.model.EyeGazePosition
import com.oojohn.vision.domain.model.ReadingSettings
import com.oojohn.vision.domain.model.ScrollDirection
import com.oojohn.vision.domain.model.SensitivityLevel
import com.oojohn.vision.domain.model.UIState
import com.oojohn.vision.domain.repository.CameraRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * 眼動追蹤使用案例，封裝眼動追蹤業務邏輯
 */
class EyeTrackingUseCase @Inject constructor(
    private val cameraRepository: CameraRepository
) {
    private var settings = ReadingSettings()

    /**
     * 相機狀態
     */
    val cameraState: StateFlow<UIState<Unit>> = cameraRepository.cameraState

    /**
     * 眼動位置
     */
    val eyeGazePosition: StateFlow<EyeGazePosition> = cameraRepository.eyeGazePosition

    suspend fun startTracking(lifecycleOwner: LifecycleOwner): Boolean =
        cameraRepository.startCamera(lifecycleOwner)

    fun stopTracking() = cameraRepository.stopCamera()

    /**
     * 設定靈敏度
     * @param sensitivity 靈敏度值 (0.0 ~ 1.0)
     */
    fun setSensitivity(sensitivity: Float) {
        cameraRepository.setEyeTrackingSensitivity(sensitivity)
    }

    fun updateSettings(newSettings: ReadingSettings) {
        this.settings = newSettings
    }

    fun getScrollDirectionFlow(): Flow<ScrollDirection> =
        cameraRepository.eyeGazePosition.map { position ->
            when (settings.sensitivity) {
                SensitivityLevel.LOW -> mapPositionToScrollDirectionLowSensitivity(position)
                SensitivityLevel.MEDIUM -> mapPositionToScrollDirection(position)
                SensitivityLevel.HIGH -> mapPositionToScrollDirectionHighSensitivity(position)
            }
        }

    fun getEyeGazePositionFlow(): Flow<EyeGazePosition> =
        cameraRepository.eyeGazePosition

    private fun mapPositionToScrollDirection(position: EyeGazePosition): ScrollDirection =
        when (position) {
            is EyeGazePosition.Top -> ScrollDirection.UP
            is EyeGazePosition.Bottom -> ScrollDirection.DOWN
            else -> ScrollDirection.NONE
        }

    private fun mapPositionToScrollDirectionLowSensitivity(position: EyeGazePosition): ScrollDirection =
        when (position) {
            is EyeGazePosition.Top -> ScrollDirection.UP
            is EyeGazePosition.Bottom -> ScrollDirection.DOWN
            else -> ScrollDirection.NONE
        }

    private fun mapPositionToScrollDirectionHighSensitivity(position: EyeGazePosition): ScrollDirection =
        when (position) {
            is EyeGazePosition.Top -> ScrollDirection.UP
            is EyeGazePosition.Bottom -> ScrollDirection.DOWN
            else -> ScrollDirection.NONE
        }
}
