package com.oojohn.vision.domain.usecase

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 權限管理使用案例
 * 處理應用程式所需的各種權限請求和狀態管理
 */
@Singleton
class PermissionUseCase @Inject constructor(
    private val context: Context
) {
    
    private val _cameraPermissionGranted = MutableStateFlow(false)
    val cameraPermissionGranted: StateFlow<Boolean> = _cameraPermissionGranted.asStateFlow()
    
    private val _permissionDeniedPermanently = MutableStateFlow(false)
    val permissionDeniedPermanently: StateFlow<Boolean> = _permissionDeniedPermanently.asStateFlow()
    
    /**
     * 檢查相機權限是否已授予
     * @return 權限狀態
     */
    fun checkCameraPermission(): Boolean {
        val isGranted = ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
        
        _cameraPermissionGranted.value = isGranted
        return isGranted
    }
    
    /**
     * 更新相機權限狀態
     * @param granted 是否已授予權限
     */
    fun updateCameraPermissionStatus(granted: Boolean) {
        _cameraPermissionGranted.value = granted
    }
    
    /**
     * 設定權限永久拒絕狀態
     * @param denied 是否永久拒絕
     */
    fun setPermissionDeniedPermanently(denied: Boolean) {
        _permissionDeniedPermanently.value = denied
    }
    
    /**
     * 取得所需權限列表
     * @return 權限列表
     */
    fun getRequiredPermissions(): Array<String> {
        return arrayOf(
            Manifest.permission.CAMERA
        )
    }
    
    /**
     * 檢查是否所有必要權限都已授予
     * @return 是否已授予所有權限
     */
    fun areAllPermissionsGranted(): Boolean {
        return checkCameraPermission()
    }
    
    /**
     * 重置權限狀態
     */
    fun resetPermissionStates() {
        _permissionDeniedPermanently.value = false
        checkCameraPermission()
    }
}