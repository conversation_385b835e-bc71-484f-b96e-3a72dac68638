package com.oojohn.vision.domain.usecase

import androidx.compose.foundation.ScrollState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.oojohn.vision.domain.model.EyeGazePosition
import com.oojohn.vision.domain.model.ReadingSettings
import com.oojohn.vision.domain.model.ScrollConfig
import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 智能滾動控制器
 * 處理眼動控制的智能滾動邏輯，讓下方文字移動到眼睛居中位置
 */
@Singleton
class SmartScrollController @Inject constructor() {

    /**
     * 計算智能滾動距離
     * @param gazePosition 眼動位置
     * @param scrollState 當前滾動狀態
     * @param screenHeight 螢幕高度（像素）
     * @param settings 閱讀設定
     * @return 目標滾動位置
     */
    suspend fun calculateSmartScrollTarget(
        gazePosition: EyeGazePosition,
        scrollState: ScrollState,
        screenHeight: Int,
        settings: ReadingSettings = ReadingSettings()
    ): Int? {
        return when (gazePosition) {
            EyeGazePosition.Top -> {
                // 向上滾動：使用較小的滾動距離
                val targetPosition = (scrollState.value - settings.upScrollDistance)
                    .coerceAtLeast(0)
                targetPosition
            }
            
            EyeGazePosition.Bottom -> {
                if (settings.smartScrollEnabled) {
                    // 智能向下滾動：讓下方文字移動到螢幕中央
                    calculateSmartDownScroll(scrollState, screenHeight, settings)
                } else {
                    // 普通向下滾動：使用較大的滾動距離
                    val targetPosition = (scrollState.value + settings.downScrollDistance)
                        .coerceAtMost(scrollState.maxValue)
                    targetPosition
                }
            }
            
            else -> null // Center 和 Unknown 不執行滾動
        }
    }

    /**
     * 計算智能向下滾動的目標位置
     * 目標：讓使用者眼睛看到的下方文字移動到螢幕中央
     */
    private fun calculateSmartDownScroll(
        scrollState: ScrollState,
        screenHeight: Int,
        settings: ReadingSettings
    ): Int {
        // 計算螢幕中央位置
        val screenCenter = screenHeight / 2
        
        // 計算使用者眼睛看到的下方區域（螢幕下方 1/3 區域）
        val bottomViewArea = screenHeight * 0.75f // 螢幕下方 75% 位置
        
        // 計算需要滾動的距離，讓下方文字移動到中央
        val scrollDistance = (bottomViewArea - screenCenter).toInt()
        
        // 應用設定的滾動距離調整
        val adjustedDistance = (scrollDistance * (settings.downScrollDistance / 200f)).toInt()
        
        // 計算目標位置
        val targetPosition = (scrollState.value + adjustedDistance)
            .coerceAtMost(scrollState.maxValue)
            
        return targetPosition
    }

    /**
     * 執行平滑滾動
     * @param scrollState 滾動狀態
     * @param targetPosition 目標位置
     * @param config 滾動配置
     */
    suspend fun performSmoothScroll(
        scrollState: ScrollState,
        targetPosition: Int,
        config: ScrollConfig = ScrollConfig()
    ) {
        try {
            // 執行動畫滾動
            scrollState.animateScrollTo(targetPosition)
            
            // 防止過度滾動的延遲
            delay(200)
            
        } catch (e: Exception) {
            android.util.Log.e("SmartScrollController", "滾動執行失敗", e)
        }
    }

    /**
     * 根據靈敏度調整滾動距離
     */
    fun adjustScrollDistanceForSensitivity(
        baseDistance: Int,
        sensitivity: com.oojohn.vision.domain.model.SensitivityLevel
    ): Int {
        return when (sensitivity) {
            com.oojohn.vision.domain.model.SensitivityLevel.LOW -> (baseDistance * 0.7f).toInt()
            com.oojohn.vision.domain.model.SensitivityLevel.MEDIUM -> baseDistance
            com.oojohn.vision.domain.model.SensitivityLevel.HIGH -> (baseDistance * 1.3f).toInt()
        }
    }

    /**
     * 檢查是否可以滾動
     */
    fun canScroll(scrollState: ScrollState, direction: EyeGazePosition): Boolean {
        return when (direction) {
            EyeGazePosition.Top -> scrollState.value > 0
            EyeGazePosition.Bottom -> scrollState.value < scrollState.maxValue
            else -> false
        }
    }
}

/**
 * Composable 函數：創建智能滾動配置
 */
@Composable
fun rememberSmartScrollConfig(
    settings: ReadingSettings = ReadingSettings()
): ScrollConfig {
    val density = LocalDensity.current
    val configuration = LocalConfiguration.current
    
    return remember(settings, configuration) {
        ScrollConfig(
            upDistance = settings.upScrollDistance,
            downDistance = settings.downScrollDistance,
            smartScrollEnabled = settings.smartScrollEnabled,
            animationDuration = 300L
        )
    }
}
