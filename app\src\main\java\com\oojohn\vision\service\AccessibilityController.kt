package com.oojohn.vision.service

import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.view.accessibility.AccessibilityManager

/**
 * 無障礙控制器
 * 處理系統級滾動操作和無障礙服務管理
 */
class AccessibilityController(private val context: Context) {
    
    private val accessibilityManager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
    
    /**
     * 檢查無障礙服務是否已啟用
     */
    fun isAccessibilityServiceEnabled(): Boolean {
        val enabledServices = Settings.Secure.getString(
            context.contentResolver,
            Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
        )
        
        val serviceName = "${context.packageName}/${EyeControlAccessibilityService::class.java.name}"
        return enabledServices?.contains(serviceName) == true
    }
    
    /**
     * 開啟無障礙設定頁面
     */
    fun openAccessibilitySettings() {
        val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        context.startActivity(intent)
    }
    
    /**
     * 向上滾動
     */
    fun scrollUp() {
        EyeControlAccessibilityService.getInstance()?.performScrollUp()
    }
    
    /**
     * 向下滾動
     */
    fun scrollDown() {
        EyeControlAccessibilityService.getInstance()?.performScrollDown()
    }

    /**
     * 增強的向下滾動 - 更大的滾動距離
     * 讓下方文字移動到眼睛居中位置
     */
    fun scrollDownEnhanced() {
        EyeControlAccessibilityService.getInstance()?.performScrollDownEnhanced()
    }
    
    /**
     * 執行點擊操作
     */
    fun performClick(x: Float, y: Float) {
        EyeControlAccessibilityService.getInstance()?.performClick(x, y)
    }
    
    /**
     * 執行返回操作
     */
    fun performBack() {
        EyeControlAccessibilityService.getInstance()?.performBack()
    }
    
    /**
     * 執行首頁操作
     */
    fun performHome() {
        EyeControlAccessibilityService.getInstance()?.performHome()
    }
}