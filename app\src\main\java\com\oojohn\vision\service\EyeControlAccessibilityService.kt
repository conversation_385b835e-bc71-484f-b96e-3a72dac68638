package com.oojohn.vision.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Intent
import android.graphics.Path
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 眼動控制無障礙服務
 * 提供系統級的滾動和點擊操作能力
 */
class EyeControlAccessibilityService : AccessibilityService() {
    
    private val serviceScope = CoroutineScope(Dispatchers.Main)
    private var isEyeTrackingActive = false
    
    companion object {
        private var instance: EyeControlAccessibilityService? = null
        
        const val ACTION_START = "com.oojohn.vision.ACTION_START_EYE_TRACKING"
        const val ACTION_STOP = "com.oojohn.vision.ACTION_STOP_EYE_TRACKING"
        
        fun getInstance(): EyeControlAccessibilityService? = instance
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        android.util.Log.d("EyeTracking", "無障礙服務已連接")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let {
            when (it.action) {
                ACTION_START -> {
                    android.util.Log.d("EyeTracking", "背景服務收到啟動指令")
                    startEyeTracking()
                }
                ACTION_STOP -> {
                    android.util.Log.d("EyeTracking", "背景服務收到停止指令")
                    stopEyeTracking()
                }
            }
        }
        return START_STICKY
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // 處理無障礙事件（如果需要）
    }
    
    override fun onInterrupt() {
        // 服務中斷時的處理
        android.util.Log.d("EyeTracking", "無障礙服務中斷")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        android.util.Log.d("EyeTracking", "無障礙服務銷毀")
        stopEyeTracking()
        instance = null
    }
    
    /**
     * 啟動眼動追蹤
     */
    private fun startEyeTracking() {
        isEyeTrackingActive = true
        android.util.Log.d("EyeTracking", "背景眼動追蹤已啟動")
        // 這裡可以添加背景眼動追蹤的邏輯
        // 例如：監聽系統狀態、處理眼動指令等
    }
    
    /**
     * 停止眼動追蹤
     */
    private fun stopEyeTracking() {
        isEyeTrackingActive = false
        android.util.Log.d("EyeTracking", "背景眼動追蹤已停止")
    }
    
    /**
     * 檢查眼動追蹤是否啟用
     */
    fun isEyeTrackingActive(): Boolean = isEyeTrackingActive
    
    /**
     * 執行向上滾動
     */
    fun performScrollUp() {
        serviceScope.launch {
            val rootNode = rootInActiveWindow ?: return@launch
            
            // 嘗試找到可滾動的節點
            val scrollableNode = findScrollableNode(rootNode)
            
            if (scrollableNode != null) {
                // 使用無障礙 API 滾動
                scrollableNode.performAction(AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD)
            } else {
                // 使用手勢滾動
                performSwipeGesture(
                    startX = 500f,
                    startY = 800f,
                    endX = 500f,
                    endY = 400f
                )
            }
            
            rootNode.recycle()
        }
    }
    
    /**
     * 執行向下滾動
     */
    fun performScrollDown() {
        serviceScope.launch {
            val rootNode = rootInActiveWindow ?: return@launch

            // 嘗試找到可滾動的節點
            val scrollableNode = findScrollableNode(rootNode)

            if (scrollableNode != null) {
                // 使用無障礙 API 滾動
                scrollableNode.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD)
            } else {
                // 使用手勢滾動
                performSwipeGesture(
                    startX = 500f,
                    startY = 400f,
                    endX = 500f,
                    endY = 800f
                )
            }

            rootNode.recycle()
        }
    }

    /**
     * 執行增強的向下滾動 - 更大的滾動距離
     * 讓下方文字移動到眼睛居中位置
     */
    fun performScrollDownEnhanced() {
        serviceScope.launch {
            val rootNode = rootInActiveWindow ?: return@launch

            // 嘗試找到可滾動的節點
            val scrollableNode = findScrollableNode(rootNode)

            if (scrollableNode != null) {
                // 使用無障礙 API 進行多次滾動以達到更大的滾動距離
                repeat(2) {
                    scrollableNode.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD)
                    delay(50) // 短暫延遲確保滾動完成
                }
            } else {
                // 使用增強的手勢滾動 - 更大的滾動距離
                performEnhancedSwipeGesture(
                    startX = 500f,
                    startY = 300f,  // 從更高的位置開始
                    endX = 500f,
                    endY = 900f     // 滾動到更低的位置
                )
            }

            rootNode.recycle()
        }
    }
    
    /**
     * 執行點擊操作
     */
    fun performClick(x: Float, y: Float) {
        serviceScope.launch {
            performTapGesture(x, y)
        }
    }
    
    /**
     * 執行返回操作
     */
    fun performBack() {
        performGlobalAction(GLOBAL_ACTION_BACK)
    }
    
    /**
     * 執行首頁操作
     */
    fun performHome() {
        performGlobalAction(GLOBAL_ACTION_HOME)
    }
    
    /**
     * 尋找可滾動的節點
     */
    private fun findScrollableNode(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        if (node.isScrollable) {
            return node
        }
        
        for (i in 0 until node.childCount) {
            val child = node.getChild(i) ?: continue
            val scrollableChild = findScrollableNode(child)
            if (scrollableChild != null) {
                child.recycle()
                return scrollableChild
            }
            child.recycle()
        }
        
        return null
    }
    
    /**
     * 執行滑動手勢
     */
    private fun performSwipeGesture(startX: Float, startY: Float, endX: Float, endY: Float) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            val path = Path().apply {
                moveTo(startX, startY)
                lineTo(endX, endY)
            }

            val gestureBuilder = GestureDescription.Builder()
            val strokeDescription = GestureDescription.StrokeDescription(path, 0, 300)
            gestureBuilder.addStroke(strokeDescription)

            dispatchGesture(gestureBuilder.build(), null, null)
        }
    }

    /**
     * 執行增強的滑動手勢 - 更長的滑動距離和更慢的速度
     * 用於實現智能滾動，讓下方文字移動到中央位置
     */
    private fun performEnhancedSwipeGesture(startX: Float, startY: Float, endX: Float, endY: Float) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            val path = Path().apply {
                moveTo(startX, startY)
                lineTo(endX, endY)
            }

            val gestureBuilder = GestureDescription.Builder()
            // 使用更長的持續時間來實現更平滑的滾動
            val strokeDescription = GestureDescription.StrokeDescription(path, 0, 500)
            gestureBuilder.addStroke(strokeDescription)

            dispatchGesture(gestureBuilder.build(), null, null)
        }
    }
    
    /**
     * 執行點擊手勢
     */
    private fun performTapGesture(x: Float, y: Float) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            val path = Path().apply {
                moveTo(x, y)
            }
            
            val gestureBuilder = GestureDescription.Builder()
            val strokeDescription = GestureDescription.StrokeDescription(path, 0, 100)
            gestureBuilder.addStroke(strokeDescription)
            
            dispatchGesture(gestureBuilder.build(), null, null)
        }
    }
}