package com.oojohn.vision.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat

import com.oojohn.vision.MainActivity
import com.oojohn.vision.R
import com.oojohn.vision.domain.model.EyeGazePosition
import com.oojohn.vision.domain.usecase.EyeTrackingUseCase
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 眼動追蹤前景服務
 * 在背景持續運行眼動追蹤功能，支援系統級眼動控制
 */
@AndroidEntryPoint
class EyeTrackingService : Service() {

    @Inject
    lateinit var eyeTrackingUseCase: EyeTrackingUseCase

    private var overlayManager: OverlayManager? = null
    private var accessibilityController: AccessibilityController? = null

    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "eye_tracking_service"
        
        const val ACTION_START_TRACKING = "START_TRACKING"
        const val ACTION_STOP_TRACKING = "STOP_TRACKING"
        const val ACTION_TOGGLE_OVERLAY = "TOGGLE_OVERLAY"

        /**
         * 啟動眼動追蹤服務
         */
        fun startService(context: Context) {
            val intent = Intent(context, EyeTrackingService::class.java).apply {
                action = ACTION_START_TRACKING
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 停止眼動追蹤服務
         */
        fun stopService(context: Context) {
            val intent = Intent(context, EyeTrackingService::class.java).apply {
                action = ACTION_STOP_TRACKING
            }
            context.stopService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        overlayManager = OverlayManager(this)
        accessibilityController = AccessibilityController(this)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        
        when (intent?.action) {
            ACTION_START_TRACKING -> startEyeTracking()
            ACTION_STOP_TRACKING -> stopEyeTracking()
            ACTION_TOGGLE_OVERLAY -> toggleOverlay()
        }
        
        return START_STICKY
    }

    override fun onBind(intent: Intent): IBinder? {
        return null
    }

    /**
     * 開始眼動追蹤
     */
    private fun startEyeTracking() {
        startForeground(NOTIFICATION_ID, createNotification())
        
        // 啟動相機和眼動追蹤 (暫時註解，需要實際實作)
        // eyeTrackingUseCase.startEyeTracking(this@EyeTrackingService)
        
        // 監聽眼動位置變化 (在實際實作中需要使用 CoroutineScope)
        // eyeTrackingUseCase.eyeGazePosition.collect { gazePosition ->
        //     handleGazePosition(gazePosition)
        // }
        
        // 顯示浮動控制面板
        overlayManager?.showOverlay()
    }

    /**
     * 停止眼動追蹤
     */
    private fun stopEyeTracking() {
        // eyeTrackingUseCase.stopEyeTracking()
        overlayManager?.hideOverlay()
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
    }

    /**
     * 切換覆蓋視窗顯示
     */
    private fun toggleOverlay() {
        overlayManager?.toggleOverlay()
    }

    /**
     * 處理眼動位置變化
     */
    private fun handleGazePosition(gazePosition: EyeGazePosition) {
        // 更新覆蓋視窗顯示
        overlayManager?.updateGazePosition(gazePosition)

        // 執行滾動操作 - 使用增強的滾動邏輯
        when (gazePosition) {
            EyeGazePosition.Top -> {
                // 向上滾動：使用標準距離
                accessibilityController?.scrollUp()
            }
            EyeGazePosition.Bottom -> {
                // 向下滾動：使用增強的滾動距離，讓下方文字移到中央
                accessibilityController?.scrollDownEnhanced()
            }
            EyeGazePosition.Center -> {
                // 居中時不執行滾動
            }
            EyeGazePosition.Unknown -> {
                // 未知狀態時不執行滾動
            }
        }
    }

    /**
     * 創建通知頻道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                getString(R.string.eye_tracking_service_channel_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.eye_tracking_service_channel_description)
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 創建前景服務通知
     */
    private fun createNotification(): Notification {
        val openAppIntent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        val openAppPendingIntent = PendingIntent.getActivity(
            this, 0, openAppIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val stopIntent = Intent(this, EyeTrackingService::class.java).apply {
            action = ACTION_STOP_TRACKING
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val toggleOverlayIntent = Intent(this, EyeTrackingService::class.java).apply {
            action = ACTION_TOGGLE_OVERLAY
        }
        val toggleOverlayPendingIntent = PendingIntent.getService(
            this, 1, toggleOverlayIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.eye_tracking_service_title))
            .setContentText(getString(R.string.eye_tracking_service_description))
            .setSmallIcon(R.drawable.ic_eye_tracking)
            .setContentIntent(openAppPendingIntent)
            .setOngoing(true)
            .addAction(
                R.drawable.ic_visibility_off,
                getString(R.string.toggle_overlay),
                toggleOverlayPendingIntent
            )
            .addAction(
                R.drawable.ic_stop,
                getString(R.string.stop_service),
                stopPendingIntent
            )
            .build()
    }

    override fun onDestroy() {
        super.onDestroy()
        overlayManager?.hideOverlay()
        // eyeTrackingUseCase.stopEyeTracking()
    }
}