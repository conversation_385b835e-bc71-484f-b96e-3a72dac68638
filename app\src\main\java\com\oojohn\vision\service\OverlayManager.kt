package com.oojohn.vision.service

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import com.oojohn.vision.R
import com.oojohn.vision.domain.model.EyeGazePosition

/**
 * 系統覆蓋視窗管理器
 * 管理浮動控制面板的顯示和隱藏
 */
class OverlayManager(private val context: Context) {
    
    private val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var overlayView: View? = null
    private var isOverlayVisible = false
    
    private lateinit var gazeIndicator: ImageView
    private lateinit var statusText: TextView
    
    /**
     * 顯示浮動覆蓋視窗
     */
    fun showOverlay() {
        if (isOverlayVisible || overlayView != null) return
        
        try {
            overlayView = createOverlayView()
            val layoutParams = createLayoutParams()
            
            windowManager.addView(overlayView, layoutParams)
            isOverlayVisible = true
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 隱藏浮動覆蓋視窗
     */
    fun hideOverlay() {
        if (!isOverlayVisible || overlayView == null) return
        
        try {
            windowManager.removeView(overlayView)
            overlayView = null
            isOverlayVisible = false
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 切換覆蓋視窗顯示狀態
     */
    fun toggleOverlay() {
        if (isOverlayVisible) {
            hideOverlay()
        } else {
            showOverlay()
        }
    }
    
    /**
     * 更新眼動位置顯示
     */
    fun updateGazePosition(gazePosition: EyeGazePosition) {
        overlayView?.let {
            gazeIndicator.setImageResource(
                when (gazePosition) {
                    EyeGazePosition.Top -> R.drawable.ic_keyboard_arrow_up
                    EyeGazePosition.Center -> R.drawable.ic_remove
                    EyeGazePosition.Bottom -> R.drawable.ic_keyboard_arrow_down
                    EyeGazePosition.Unknown -> R.drawable.ic_help_outline
                }
            )
            
            statusText.text = when (gazePosition) {
                EyeGazePosition.Top -> context.getString(R.string.gaze_position_top)
                EyeGazePosition.Center -> context.getString(R.string.gaze_position_center)
                EyeGazePosition.Bottom -> context.getString(R.string.gaze_position_bottom)
                EyeGazePosition.Unknown -> context.getString(R.string.gaze_position_unknown)
            }
        }
    }
    
    /**
     * 創建覆蓋視窗佈局
     */
    private fun createOverlayView(): View {
        val inflater = LayoutInflater.from(context)
        val view = inflater.inflate(R.layout.overlay_eye_tracking, null)
        
        gazeIndicator = view.findViewById(R.id.gaze_indicator)
        statusText = view.findViewById(R.id.status_text)
        
        // 設定點擊事件
        view.setOnClickListener {
            // 點擊時切換顯示詳細資訊
            toggleDetailedView()
        }
        
        return view
    }
    
    /**
     * 創建視窗佈局參數
     */
    private fun createLayoutParams(): WindowManager.LayoutParams {
        val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }
        
        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            layoutFlag,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.END
            x = 20
            y = 100
        }
    }
    
    /**
     * 切換詳細視圖顯示
     */
    private fun toggleDetailedView() {
        // 這裡可以實作展開/收縮詳細資訊的邏輯
        statusText.visibility = if (statusText.visibility == View.VISIBLE) {
            View.GONE
        } else {
            View.VISIBLE
        }
    }
}