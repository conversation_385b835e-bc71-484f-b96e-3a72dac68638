package com.oojohn.vision.ui.components

import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.oojohn.vision.R
import com.oojohn.vision.ui.viewmodel.PermissionViewModel

/**
 * 權限請求處理 Composable
 * 處理相機權限的請求和狀態管理
 */
@Composable
fun PermissionHandler(
    onPermissionGranted: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: PermissionViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val permissionGranted by viewModel.cameraPermissionGranted.collectAsStateWithLifecycle()
    val permissionDeniedPermanently by viewModel.permissionDeniedPermanently.collectAsStateWithLifecycle()

    // 權限請求啟動器
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val cameraPermissionGranted = permissions[android.Manifest.permission.CAMERA] ?: false
        
        if (cameraPermissionGranted) {
            viewModel.updateCameraPermissionStatus(true)
            onPermissionGranted()
        } else {
            viewModel.setPermissionDeniedPermanently(true)
        }
    }

    LaunchedEffect(Unit) {
        if (viewModel.checkCameraPermission()) {
            onPermissionGranted()
        }
    }

    if (!permissionGranted) {
        PermissionRequestContent(
            permissionDeniedPermanently = permissionDeniedPermanently,
            onRequestPermission = {
                permissionLauncher.launch(viewModel.getRequiredPermissions())
            },
            onOpenSettings = {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.fromParts("package", context.packageName, null)
                }
                context.startActivity(intent)
            },
            modifier = modifier
        )
    }
}

/**
 * 權限請求內容顯示
 */
@Composable
private fun PermissionRequestContent(
    permissionDeniedPermanently: Boolean,
    onRequestPermission: () -> Unit,
    onOpenSettings: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = stringResource(R.string.camera_permission_required),
                style = MaterialTheme.typography.headlineSmall,
                textAlign = TextAlign.Center
            )

            Text(
                text = if (permissionDeniedPermanently) {
                    stringResource(R.string.camera_permission_denied_message)
                } else {
                    stringResource(R.string.camera_permission_description)
                },
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            if (permissionDeniedPermanently) {
                Button(
                    onClick = onOpenSettings,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(stringResource(R.string.open_settings))
                }
            } else {
                Button(
                    onClick = onRequestPermission,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(stringResource(R.string.grant_permission))
                }
            }
        }
    }
}

/**
 * 權限狀態指示器
 */
@Composable
fun PermissionStatusIndicator(
    isGranted: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = if (isGranted) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.errorContainer
            }
        )
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = if (isGranted) {
                    stringResource(R.string.camera_permission_granted)
                } else {
                    stringResource(R.string.camera_permission_denied)
                },
                style = MaterialTheme.typography.bodySmall,
                color = if (isGranted) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onErrorContainer
                }
            )
        }
    }
}