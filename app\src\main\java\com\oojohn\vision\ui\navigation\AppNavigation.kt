package com.oojohn.vision.ui.navigation

import androidx.compose.runtime.Composable
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.oojohn.vision.ui.screen.ArticleDetailScreen
import com.oojohn.vision.ui.screen.ArticleListScreen
import com.oojohn.vision.ui.screen.BackgroundServiceScreen
import com.oojohn.vision.ui.screen.EyeTrackingScreen
import com.oojohn.vision.ui.screen.MainScreen
import com.oojohn.vision.ui.screen.SettingsScreen
import com.oojohn.vision.ui.viewmodel.EyeTrackingViewModel
import com.oojohn.vision.ui.viewmodel.ArticleViewModel

/**
 * 應用程式導航路由定義
 */
object AppDestinations {
    const val MAIN = "main"
    const val EYE_TRACKING = "eye_tracking"
    const val ARTICLE_LIST = "article_list"
    const val ARTICLE_DETAIL = "article_detail"
    const val SETTINGS = "settings"
    const val BACKGROUND_SERVICE = "background_service"
}

/**
 * 應用程式主要導航架構
 * @param navController 導航控制器
 */
@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController()
) {
    // 在頂層創建共用的 ViewModel
    val eyeTrackingViewModel: EyeTrackingViewModel = hiltViewModel()
    val articleViewModel: ArticleViewModel = hiltViewModel()
    
    NavHost(
        navController = navController,
        startDestination = AppDestinations.MAIN
    ) {
        composable(AppDestinations.MAIN) {
            MainScreen(navController = navController)
        }
        
        composable(AppDestinations.EYE_TRACKING) {
            EyeTrackingScreen(
                navController = navController,
                eyeTrackingViewModel = eyeTrackingViewModel
            )
        }
        
        composable(AppDestinations.ARTICLE_LIST) {
            ArticleListScreen(
                navController = navController,
                viewModel = articleViewModel
            )
        }

        composable(AppDestinations.ARTICLE_DETAIL) {
            ArticleDetailScreen(
                navController = navController,
                articleViewModel = articleViewModel,
                eyeTrackingViewModel = eyeTrackingViewModel
            )
        }
        
        composable(AppDestinations.SETTINGS) {
            SettingsScreen(navController = navController)
        }
        
        composable(AppDestinations.BACKGROUND_SERVICE) {
            BackgroundServiceScreen(navController = navController)
        }
    }
} 