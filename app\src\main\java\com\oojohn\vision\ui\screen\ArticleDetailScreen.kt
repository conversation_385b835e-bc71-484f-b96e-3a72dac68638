package com.oojohn.vision.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.oojohn.vision.R
import com.oojohn.vision.domain.model.EyeGazePosition
import com.oojohn.vision.domain.model.ReadingSettings
import com.oojohn.vision.domain.usecase.SmartScrollController
import com.oojohn.vision.ui.viewmodel.ArticleViewModel
import com.oojohn.vision.ui.viewmodel.EyeTrackingViewModel
import com.oojohn.vision.ui.viewmodel.PermissionViewModel
import kotlinx.coroutines.delay

/**
 * 文章詳細閱讀畫面
 * 整合眼動追蹤與文章閱讀功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ArticleDetailScreen(
    navController: NavController,
    articleViewModel: ArticleViewModel,
    eyeTrackingViewModel: EyeTrackingViewModel,
    permissionViewModel: PermissionViewModel = hiltViewModel()
) {
    val selectedArticle by articleViewModel.selectedArticle.collectAsState()
    val scrollPosition by articleViewModel.currentScrollPosition.collectAsState()
    val eyeGazePosition by eyeTrackingViewModel.eyeGazePosition.collectAsState()
    val isEyeTrackingEnabled by eyeTrackingViewModel.isTracking.collectAsState()
    val cameraPermissionGranted by permissionViewModel.cameraPermissionGranted.collectAsState()

    val scrollState = rememberScrollState()
    val lifecycleOwner = LocalLifecycleOwner.current
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    // 創建智能滾動控制器實例
    val smartScrollController = remember { SmartScrollController() }

    // 智能滾動設定
    val readingSettings = remember {
        ReadingSettings(
            upScrollDistance = 100,
            downScrollDistance = 250,  // 增加向下滾動距離
            smartScrollEnabled = true
        )
    }

    // 計算螢幕高度（像素）
    val screenHeightPx = remember(configuration) {
        with(density) { configuration.screenHeightDp.dp.toPx().toInt() }
    }

    // 確保眼動追蹤在進入文章頁面時自動啟動
    LaunchedEffect(cameraPermissionGranted) {
        android.util.Log.d("EyeTracking", "ArticleDetailScreen 初始化，相機權限: $cameraPermissionGranted, 眼動追蹤狀態: $isEyeTrackingEnabled")

        if (cameraPermissionGranted) {
            // 無論當前狀態如何，都重新啟動眼動追蹤以確保正常工作
            android.util.Log.d("EyeTracking", "權限已授予，強制重新啟動眼動追蹤")
            eyeTrackingViewModel.startEyeTracking(lifecycleOwner)
        } else {
            android.util.Log.w("EyeTracking", "相機權限未授予，無法啟動眼動追蹤")
        }
    }

    // 額外的保險機制：延遲檢查眼動追蹤狀態
    LaunchedEffect(Unit) {
        delay(1000) // 等待 1 秒
        if (cameraPermissionGranted && !isEyeTrackingEnabled) {
            android.util.Log.d("EyeTracking", "延遲檢查：眼動追蹤未啟動，重新嘗試啟動")
            eyeTrackingViewModel.startEyeTracking(lifecycleOwner)
        }
    }

    // 添加狀態監控
    LaunchedEffect(Unit) {
        android.util.Log.d("EyeTracking", "ArticleDetailScreen 組件已創建")
    }

    // 監控狀態變化
    LaunchedEffect(eyeGazePosition) {
        android.util.Log.d("EyeTracking", "ArticleDetailScreen - 眼動位置變化: $eyeGazePosition")
    }

    LaunchedEffect(isEyeTrackingEnabled) {
        android.util.Log.d("EyeTracking", "ArticleDetailScreen - 追蹤狀態變化: $isEyeTrackingEnabled")
    }

    LaunchedEffect(scrollState.maxValue) {
        android.util.Log.d("EyeTracking", "ArticleDetailScreen - 滾動最大值變化: ${scrollState.maxValue}")
    }

    // 智能眼動控制滾動邏輯
    LaunchedEffect(eyeGazePosition, isEyeTrackingEnabled) {
        android.util.Log.d("EyeTracking", "ArticleDetailScreen - 智能滾動邏輯觸發")
        android.util.Log.d("EyeTracking", "  - 眼動位置: $eyeGazePosition")
        android.util.Log.d("EyeTracking", "  - 追蹤啟用: $isEyeTrackingEnabled")
        android.util.Log.d("EyeTracking", "  - 滾動最大值: ${scrollState.maxValue}")
        android.util.Log.d("EyeTracking", "  - 當前滾動位置: ${scrollState.value}")
        android.util.Log.d("EyeTracking", "  - 螢幕高度: ${screenHeightPx}px")

        if (isEyeTrackingEnabled && scrollState.maxValue > 0 &&
            smartScrollController.canScroll(scrollState, eyeGazePosition)) {

            // 使用智能滾動控制器計算目標位置
            val targetPosition = smartScrollController.calculateSmartScrollTarget(
                gazePosition = eyeGazePosition,
                scrollState = scrollState,
                screenHeight = screenHeightPx,
                settings = readingSettings
            )

            targetPosition?.let { target ->
                when (eyeGazePosition) {
                    EyeGazePosition.Top -> {
                        android.util.Log.d("EyeTracking", "執行智能向上滾動: ${scrollState.value} -> $target")
                        smartScrollController.performSmoothScroll(scrollState, target)
                        android.util.Log.d("EyeTracking", "向上滾動完成，新位置: ${scrollState.value}")
                    }
                    EyeGazePosition.Bottom -> {
                        android.util.Log.d("EyeTracking", "執行智能向下滾動: ${scrollState.value} -> $target")
                        android.util.Log.d("EyeTracking", "  - 滾動距離: ${target - scrollState.value}")
                        android.util.Log.d("EyeTracking", "  - 智能滾動啟用: ${readingSettings.smartScrollEnabled}")
                        smartScrollController.performSmoothScroll(scrollState, target)
                        android.util.Log.d("EyeTracking", "向下滾動完成，新位置: ${scrollState.value}")
                    }
                    else -> {
                        android.util.Log.d("EyeTracking", "眼動位置為 ${eyeGazePosition}，不執行滾動")
                    }
                }
            }
        } else {
            android.util.Log.d("EyeTracking", "滾動條件不滿足")
            android.util.Log.d("EyeTracking", "  - 追蹤啟用: $isEyeTrackingEnabled")
            android.util.Log.d("EyeTracking", "  - 滾動最大值: ${scrollState.maxValue}")
            android.util.Log.d("EyeTracking", "  - 可以滾動: ${smartScrollController.canScroll(scrollState, eyeGazePosition)}")
        }
    }

    // 更新滾動位置到 ViewModel
    LaunchedEffect(scrollState.value, scrollState.maxValue) {
        if (scrollState.maxValue > 0) {
            val position = scrollState.value.toFloat() / scrollState.maxValue.toFloat()
            articleViewModel.updateScrollPosition(position)
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = stringResource(R.string.reading_article),
                        maxLines = 1
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back_to_list)
                        )
                    }
                },
                actions = {
                    // 眼動追蹤狀態指示器 - 添加調試信息，始終顯示
                    EyeTrackingIndicator(
                        eyeGazePosition = eyeGazePosition,
                        isTracking = isEyeTrackingEnabled
                    )
                    
                    // 眼動追蹤開關
                    IconButton(
                        onClick = {
                            eyeTrackingViewModel.toggleEyeTracking(lifecycleOwner)
                        }
                    ) {
                        Icon(
                            imageVector = if (isEyeTrackingEnabled) Icons.Default.Face else Icons.Default.Close,
                            contentDescription = if (isEyeTrackingEnabled) "停止眼動追蹤" else "啟動眼動追蹤",
                            tint = if (isEyeTrackingEnabled) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            )
        },
        bottomBar = {
            if (isEyeTrackingEnabled) {
                EyeTrackingStatusBar(
                    eyeGazePosition = eyeGazePosition,
                    scrollPosition = scrollPosition.toInt()
                )
            }
        }
    ) { paddingValues ->
        selectedArticle?.let { article ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .verticalScroll(scrollState)
                    .padding(16.dp)
            ) {
                // 文章標題
                Text(
                    text = article.title,
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 文章資訊
                ArticleInfo(
                    author = article.author,
                    datePublished = article.datePublished,
                    estimatedReadingTime = article.estimatedReadingTime
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                HorizontalDivider()
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 文章內容
                Text(
                    text = article.content,
                    style = MaterialTheme.typography.bodyLarge,
                    lineHeight = MaterialTheme.typography.bodyLarge.lineHeight * 1.6,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(48.dp))
            }
        } ?: run {
            // 沒有選擇文章時的佔位符
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "沒有選擇文章",
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

/**
 * 眼動追蹤指示器 - 顯示在右上角
 */
@Composable
private fun EyeTrackingIndicator(
    eyeGazePosition: EyeGazePosition,
    isTracking: Boolean
) {
    // 記錄狀態變化 - 增加更詳細的日誌
    LaunchedEffect(eyeGazePosition, isTracking) {
        android.util.Log.d("EyeTracking", "UI指示器更新: 位置=$eyeGazePosition, 追蹤狀態=$isTracking, 時間=${System.currentTimeMillis()}")
    }

    val (icon, color, description) = when (eyeGazePosition) {
        EyeGazePosition.Top -> Triple(
            Icons.Default.KeyboardArrowUp,
            MaterialTheme.colorScheme.error,
            "向上看"
        )
        EyeGazePosition.Bottom -> Triple(
            Icons.Default.KeyboardArrowDown,
            MaterialTheme.colorScheme.primary,
            "向下看"
        )
        EyeGazePosition.Center -> Triple(
            Icons.Default.PlayArrow,
            MaterialTheme.colorScheme.secondary,
            "中央位置"
        )
        EyeGazePosition.Unknown -> Triple(
            Icons.Default.Search,
            MaterialTheme.colorScheme.outline,
            "偵測中"
        )
    }
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Card(
            modifier = Modifier.size(40.dp),
            colors = CardDefaults.cardColors(
                containerColor = if (isTracking) color.copy(alpha = 0.1f) else MaterialTheme.colorScheme.surfaceVariant
            ),
            border = androidx.compose.foundation.BorderStroke(
                1.dp, 
                if (isTracking) color.copy(alpha = 0.3f) else MaterialTheme.colorScheme.outline
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = description,
                    tint = if (isTracking) color else MaterialTheme.colorScheme.outline,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
        
        // 調試信息 - 顯示當前狀態
        Text(
            text = "${getPositionName(eyeGazePosition)}${if (isTracking) " ✓" else " ✗"}",
            style = MaterialTheme.typography.labelSmall,
            color = if (isTracking) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.outline,
            modifier = Modifier.padding(top = 2.dp)
        )
        
        // 額外的狀態信息
        Text(
            text = if (isTracking) "追蹤中" else "已停止",
            style = MaterialTheme.typography.labelSmall.copy(fontSize = MaterialTheme.typography.labelSmall.fontSize * 0.8f),
            color = if (isTracking) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.outline,
            modifier = Modifier.padding(top = 1.dp)
        )
    }
}

/**
 * 文章資訊區塊
 */
@Composable
private fun ArticleInfo(
    author: String,
    datePublished: String,
    estimatedReadingTime: Int
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 作者
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Person,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = author,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 閱讀時間
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Star,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = "$estimatedReadingTime 分鐘",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 發布日期
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .padding(bottom = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.DateRange,
                contentDescription = null,
                modifier = Modifier.size(18.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.width(6.dp))
            Text(
                text = datePublished,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 眼動追蹤狀態列
 */
@Composable
private fun EyeTrackingStatusBar(
    eyeGazePosition: EyeGazePosition,
    scrollPosition: Int
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surfaceVariant,
        tonalElevation = 4.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "眼動追蹤: ${getPositionName(eyeGazePosition)}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = "滾動位置: $scrollPosition",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 獲取眼動位置的名稱
 */
private fun getPositionName(position: EyeGazePosition): String {
    return when (position) {
        is EyeGazePosition.Top -> "向上"
        is EyeGazePosition.Center -> "中央"
        is EyeGazePosition.Bottom -> "向下"
        is EyeGazePosition.Unknown -> "未知"
    }
}