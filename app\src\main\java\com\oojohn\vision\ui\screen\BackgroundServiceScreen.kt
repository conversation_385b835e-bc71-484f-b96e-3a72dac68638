package com.oojohn.vision.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.oojohn.vision.R
import com.oojohn.vision.ui.viewmodel.BackgroundServiceViewModel

/**
 * 背景服務控制畫面
 * 管理背景眼動追蹤服務的啟動、停止和權限設定
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BackgroundServiceScreen(
    navController: NavController,
    viewModel: BackgroundServiceViewModel = hiltViewModel()
) {
    val isServiceRunning by viewModel.isServiceRunning.collectAsStateWithLifecycle()
    val overlayPermissionGranted by viewModel.overlayPermissionGranted.collectAsStateWithLifecycle()
    val accessibilityServiceEnabled by viewModel.accessibilityServiceEnabled.collectAsStateWithLifecycle()

    // 定期檢查權限狀態
    LaunchedEffect(Unit) {
        viewModel.checkAllPermissions()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("背景眼動追蹤") },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 服務狀態卡片
            ServiceStatusCard(
                isServiceRunning = isServiceRunning,
                onStartService = { viewModel.startBackgroundService() },
                onStopService = { viewModel.stopBackgroundService() }
            )

            // 權限檢查區域
            PermissionsSection(
                overlayPermissionGranted = overlayPermissionGranted,
                accessibilityServiceEnabled = accessibilityServiceEnabled,
                onRequestOverlayPermission = { viewModel.requestOverlayPermission() },
                onOpenAccessibilitySettings = { viewModel.openAccessibilitySettings() },
                onRefreshPermissions = { viewModel.checkAllPermissions() }
            )

            // 使用說明
            UsageInstructionsCard()
        }
    }
}

/**
 * 服務狀態卡片
 */
@Composable
private fun ServiceStatusCard(
    isServiceRunning: Boolean,
    onStartService: () -> Unit,
    onStopService: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isServiceRunning) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Icon(
                imageVector = if (isServiceRunning) Icons.Default.PlayArrow else Icons.Default.Close,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = if (isServiceRunning) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                }
            )

            Text(
                text = if (isServiceRunning) "背景服務運行中" else "背景服務已停止",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Text(
                text = if (isServiceRunning) {
                    "眼動追蹤正在背景運行，您可以在其他應用程式中使用眼動控制"
                } else {
                    "啟動背景服務以在其他應用程式中使用眼動控制"
                },
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Button(
                onClick = if (isServiceRunning) onStopService else onStartService,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isServiceRunning) {
                        MaterialTheme.colorScheme.error
                    } else {
                        MaterialTheme.colorScheme.primary
                    }
                )
            ) {
                Icon(
                    imageVector = if (isServiceRunning) Icons.Default.Close else Icons.Default.PlayArrow,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = if (isServiceRunning) {
                        stringResource(R.string.stop_background_service)
                    } else {
                        stringResource(R.string.start_background_service)
                    }
                )
            }
        }
    }
}

/**
 * 權限檢查區域
 */
@Composable
private fun PermissionsSection(
    overlayPermissionGranted: Boolean,
    accessibilityServiceEnabled: Boolean,
    onRequestOverlayPermission: () -> Unit,
    onOpenAccessibilitySettings: () -> Unit,
    onRefreshPermissions: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "權限檢查",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )

                IconButton(onClick = onRefreshPermissions) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "重新檢查權限"
                    )
                }
            }

            // 覆蓋權限
            PermissionItem(
                title = "顯示在其他應用程式上層",
                description = "允許在其他應用程式中顯示眼動控制面板",
                isGranted = overlayPermissionGranted,
                onRequestPermission = onRequestOverlayPermission
            )

            // 無障礙服務權限
            PermissionItem(
                title = "無障礙服務",
                description = "允許在其他應用程式中執行滾動操作",
                isGranted = accessibilityServiceEnabled,
                onRequestPermission = onOpenAccessibilitySettings
            )
        }
    }
}

/**
 * 權限項目
 */
@Composable
private fun PermissionItem(
    title: String,
    description: String,
    isGranted: Boolean,
    onRequestPermission: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        if (isGranted) {
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = "已授予",
                tint = MaterialTheme.colorScheme.primary
            )
        } else {
            OutlinedButton(
                onClick = onRequestPermission
            ) {
                Text("授予")
            }
        }
    }
}

/**
 * 使用說明卡片
 */
@Composable
private fun UsageInstructionsCard() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "使用說明",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Text(
                text = """
                1. 確保所有權限都已授予
                2. 啟動背景服務
                3. 在其他應用程式中，右上角會顯示眼動控制面板
                4. 向上看可向上滾動，向下看可向下滾動
                5. 點擊控制面板可切換顯示詳細資訊
                6. 在通知欄中可以停止服務或切換顯示
                """.trimIndent(),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}