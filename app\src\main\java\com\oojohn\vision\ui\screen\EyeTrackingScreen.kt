package com.oojohn.vision.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.oojohn.vision.R
import com.oojohn.vision.domain.model.EyeGazePosition
import com.oojohn.vision.domain.model.UIState
import com.oojohn.vision.ui.components.PermissionHandler
import com.oojohn.vision.ui.viewmodel.EyeTrackingViewModel

/**
 * 眼動追蹤功能畫面
 * 顯示相機預覽和眼動追蹤結果
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EyeTrackingScreen(
    navController: NavController,
    eyeTrackingViewModel: EyeTrackingViewModel
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraState by eyeTrackingViewModel.cameraState.collectAsState()
    val eyeGazePosition by eyeTrackingViewModel.eyeGazePosition.collectAsState()
    val isTracking by eyeTrackingViewModel.isTracking.collectAsState()
    
    var sensitivity by remember { mutableStateOf(0.5f) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.start_eye_tracking)) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back_to_list)
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        var permissionGranted by remember { mutableStateOf(false) }
        
        // 權限處理包裝器
        if (!permissionGranted) {
            PermissionHandler(
                onPermissionGranted = {
                    permissionGranted = true
                },
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            )
        }
        
        // 權限授予後顯示主要內容
        if (permissionGranted) {
            EyeTrackingContent(
                paddingValues = paddingValues,
                cameraState = cameraState,
                eyeGazePosition = eyeGazePosition,
                sensitivity = sensitivity,
                isTracking = isTracking,
                onSensitivityChange = { newSensitivity ->
                    sensitivity = newSensitivity
                    eyeTrackingViewModel.setSensitivity(newSensitivity)
                },
                onStartTracking = {
                    eyeTrackingViewModel.startEyeTracking(lifecycleOwner)
                },
                onStopTracking = {
                    eyeTrackingViewModel.stopEyeTracking()
                }
            )
        }
    }
}

/**
 * 眼動追蹤主要內容
 */
@Composable
private fun EyeTrackingContent(
    paddingValues: PaddingValues,
    cameraState: UIState<Unit>,
    eyeGazePosition: EyeGazePosition,
    sensitivity: Float,
    isTracking: Boolean,
    onSensitivityChange: (Float) -> Unit,
    onStartTracking: () -> Unit,
    onStopTracking: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(paddingValues)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 相機狀態顯示
        CameraStatusCard(cameraState = cameraState)
        
        // 眼動位置顯示
        EyeGazeIndicator(eyeGazePosition = eyeGazePosition)
        
        // 靈敏度控制
        SensitivityControl(
            sensitivity = sensitivity,
            onSensitivityChange = onSensitivityChange
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 控制按鈕
        TrackingControlButtons(
            isTracking = isTracking,
            onStartTracking = onStartTracking,
            onStopTracking = onStopTracking
        )
    }
}

/**
 * 相機狀態卡片
 */
@Composable
private fun CameraStatusCard(cameraState: UIState<Unit>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (cameraState) {
                is UIState.Success -> MaterialTheme.colorScheme.primaryContainer
                is UIState.Error -> MaterialTheme.colorScheme.errorContainer
                is UIState.Loading -> MaterialTheme.colorScheme.secondaryContainer
                UIState.Idle -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = when (cameraState) {
                    is UIState.Success -> Icons.Default.CheckCircle
                    is UIState.Error -> Icons.Default.Close
                    is UIState.Loading -> Icons.Default.Refresh
                    UIState.Idle -> Icons.Default.Face
                },
                contentDescription = null,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Text(
                text = when (cameraState) {
                    is UIState.Success -> "相機已就緒"
                    is UIState.Error -> cameraState.message
                    is UIState.Loading -> stringResource(R.string.loading)
                    UIState.Idle -> "等待啟動相機"
                },
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

/**
 * 眼動位置指示器
 */
@Composable
private fun EyeGazeIndicator(eyeGazePosition: EyeGazePosition) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "眼動位置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 視覺指示器
            EyePositionVisualIndicator(eyeGazePosition)
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = when (eyeGazePosition) {
                    EyeGazePosition.Top -> stringResource(R.string.gaze_position_top)
                    EyeGazePosition.Center -> stringResource(R.string.gaze_position_center)
                    EyeGazePosition.Bottom -> stringResource(R.string.gaze_position_bottom)
                    EyeGazePosition.Unknown -> stringResource(R.string.gaze_position_unknown)
                },
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 眼動位置視覺指示器
 */
@Composable
private fun EyePositionVisualIndicator(eyeGazePosition: EyeGazePosition) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 上方指示點
        Box(
            modifier = Modifier
                .size(16.dp)
                .background(
                    color = if (eyeGazePosition == EyeGazePosition.Top) 
                        MaterialTheme.colorScheme.primary 
                    else MaterialTheme.colorScheme.outline,
                    shape = CircleShape
                )
        )
        
        // 中央指示點
        Box(
            modifier = Modifier
                .size(16.dp)
                .background(
                    color = if (eyeGazePosition == EyeGazePosition.Center) 
                        MaterialTheme.colorScheme.primary 
                    else MaterialTheme.colorScheme.outline,
                    shape = CircleShape
                )
        )
        
        // 下方指示點
        Box(
            modifier = Modifier
                .size(16.dp)
                .background(
                    color = if (eyeGazePosition == EyeGazePosition.Bottom) 
                        MaterialTheme.colorScheme.primary 
                    else MaterialTheme.colorScheme.outline,
                    shape = CircleShape
                )
        )
    }
}

/**
 * 靈敏度控制
 */
@Composable
private fun SensitivityControl(
    sensitivity: Float,
    onSensitivityChange: (Float) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = stringResource(R.string.sensitivity),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Slider(
                value = sensitivity,
                onValueChange = onSensitivityChange,
                valueRange = 0.1f..1.0f,
                steps = 8,
                modifier = Modifier.fillMaxWidth()
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.sensitivity_low),
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = stringResource(R.string.sensitivity_high),
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

/**
 * 追蹤控制按鈕
 */
@Composable
private fun TrackingControlButtons(
    isTracking: Boolean,
    onStartTracking: () -> Unit,
    onStopTracking: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        if (isTracking) {
            Button(
                onClick = onStopTracking,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(stringResource(R.string.stop_eye_tracking))
            }
        } else {
            Button(
                onClick = onStartTracking,
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(stringResource(R.string.start_eye_tracking))
            }
        }
    }
} 