package com.oojohn.vision.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.oojohn.vision.R

/**
 * 設定畫面
 * 提供應用程式相關設定選項
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(navController: NavController) {
    var isDarkThemeEnabled by remember { mutableStateOf(false) }
    var autoScrollSensitivity by remember { mutableStateOf(0.5f) }
    var enableHapticFeedback by remember { mutableStateOf(true) }
    var debugMode by remember { mutableStateOf(false) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.settings)) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back_to_list)
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 外觀設定
            SettingsSection(title = "外觀設定") {
                SettingsItem(
                    title = "深色主題",
                    description = "切換應用程式主題",
                    icon = Icons.Default.Star,
                    content = {
                        Switch(
                            checked = isDarkThemeEnabled,
                            onCheckedChange = { isDarkThemeEnabled = it }
                        )
                    }
                )
            }
            
            // 眼動追蹤設定
            SettingsSection(title = "眼動追蹤設定") {
                SettingsItem(
                    title = stringResource(R.string.sensitivity),
                    description = "調整眼動追蹤的靈敏度",
                    icon = Icons.Default.Settings,
                    content = {
                        Column {
                            Slider(
                                value = autoScrollSensitivity,
                                onValueChange = { autoScrollSensitivity = it },
                                valueRange = 0.1f..1.0f,
                                steps = 8,
                                modifier = Modifier.width(120.dp)
                            )
                            Text(
                                text = when {
                                    autoScrollSensitivity < 0.4f -> stringResource(R.string.sensitivity_low)
                                    autoScrollSensitivity < 0.7f -> stringResource(R.string.sensitivity_medium)
                                    else -> stringResource(R.string.sensitivity_high)
                                },
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                )
                
                SettingsItem(
                    title = "觸覺回饋",
                    description = "眼動操作時提供震動回饋",
                    icon = Icons.Default.Star,
                    content = {
                        Switch(
                            checked = enableHapticFeedback,
                            onCheckedChange = { enableHapticFeedback = it }
                        )
                    }
                )
            }
            
            // 關於應用程式
            SettingsSection(title = "關於應用程式") {
                SettingsItem(
                    title = "版本資訊",
                    description = "Eye Control v1.0.0",
                    icon = Icons.Default.Info,
                    onClick = {
                        // 顯示版本詳細資訊
                    }
                )
                
                SettingsItem(
                    title = "隱私政策",
                    description = "查看應用程式隱私政策",
                    icon = Icons.Default.Star,
                    onClick = {
                        // 開啟隱私政策
                    }
                )
                
                SettingsItem(
                    title = "開源授權",
                    description = "查看第三方程式庫授權",
                    icon = Icons.Default.Star,
                    onClick = {
                        // 顯示授權資訊
                    }
                )
            }
            
            // 開發者選項
            SettingsSection(title = "開發者選項") {
                SettingsItem(
                    title = "除錯模式",
                    description = "顯示詳細的偵錯資訊",
                    icon = Icons.Default.Settings,
                    content = {
                        Switch(
                            checked = debugMode,
                            onCheckedChange = { debugMode = it }
                        )
                    }
                )
                
                SettingsItem(
                    title = "重設所有設定",
                    description = "恢復應用程式預設設定",
                    icon = Icons.Default.Refresh,
                    onClick = {
                        // 重設設定邏輯
                        isDarkThemeEnabled = false
                        autoScrollSensitivity = 0.5f
                        enableHapticFeedback = true
                        debugMode = false
                    }
                )
            }
        }
    }
}

/**
 * 設定區塊
 */
@Composable
private fun SettingsSection(
    title: String,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            content()
        }
    }
}

/**
 * 設定項目
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SettingsItem(
    title: String,
    description: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onClick: (() -> Unit)? = null,
    content: (@Composable () -> Unit)? = null
) {
    val modifier = if (onClick != null) {
        Modifier.fillMaxWidth()
    } else {
        Modifier.fillMaxWidth()
    }
    
    Surface(
        onClick = onClick ?: {},
        modifier = modifier,
        enabled = onClick != null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(24.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            content?.let {
                Spacer(modifier = Modifier.width(16.dp))
                it()
            } ?: run {
                if (onClick != null) {
                    Icon(
                        imageVector = Icons.Default.ArrowForward,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
} 