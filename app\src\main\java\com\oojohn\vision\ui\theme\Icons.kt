package com.oojohn.vision.ui.theme

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*

/**
 * 應用程式 Icon 定義
 * 統一管理所有使用的 Material Design Icons
 */
object AppIcons {
    // 基本操作
    val Settings = Icons.Default.Settings
    val ArrowBack = Icons.Default.ArrowBack
    val ArrowForward = Icons.Default.ArrowForward
    val Refresh = Icons.Default.Refresh
    val CheckCircle = Icons.Default.CheckCircle
    val PlayArrow = Icons.Default.PlayArrow
    
    // 眼動追蹤相關
    val Eye = Icons.Default.Face
    val EyeOff = Icons.Default.Close
    val Camera = Icons.Default.Face
    val Stop = Icons.Default.Close
    
    // 文章相關
    val Article = Icons.Default.Star
    val Person = Icons.Default.Person
    val Time = Icons.Default.Star
    val Date = Icons.Default.Star
    val Error = Icons.Default.Close
    
    // 設定相關
    val Theme = Icons.Default.Star
    val Sensitivity = Icons.Default.Settings
    val Feedback = Icons.Default.Star
    val Info = Icons.Default.Info
    val Privacy = Icons.Default.Star
    val Code = Icons.Default.Star
    val Debug = Icons.Default.Settings
} 