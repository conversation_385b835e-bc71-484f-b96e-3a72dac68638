package com.oojohn.vision.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.vision.domain.model.ArticleContent
import com.oojohn.vision.domain.model.UIState
import com.oojohn.vision.domain.usecase.ArticleUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 文章閱讀 ViewModel
 * 負責管理文章載入、選擇和閱讀狀態
 */
@HiltViewModel
class ArticleViewModel @Inject constructor(
    private val articleUseCase: ArticleUseCase
) : ViewModel() {

    private val _articleListState = MutableStateFlow<UIState<List<ArticleContent>>>(UIState.Idle)
    val articleListState: StateFlow<UIState<List<ArticleContent>>> = _articleListState.asStateFlow()

    private val _selectedArticle = MutableStateFlow<ArticleContent?>(null)
    val selectedArticle: StateFlow<ArticleContent?> = _selectedArticle.asStateFlow()

    private val _currentScrollPosition = MutableStateFlow(0f)
    val currentScrollPosition: StateFlow<Float> = _currentScrollPosition.asStateFlow()

    init {
        loadArticles()
    }

    /**
     * 載入文章列表
     */
    private fun loadArticles() {
        viewModelScope.launch {
            _articleListState.value = UIState.Loading
            articleUseCase.getArticleList()
                .catch { e ->
                    _articleListState.value = UIState.Error(e.localizedMessage ?: "載入文章失敗")
                }
                .collect { articles ->
                    _articleListState.value = UIState.Success(articles)
                }
        }
    }

    /**
     * 選擇要閱讀的文章
     * @param article 選擇的文章
     */
    fun selectArticle(article: ArticleContent) {
        _selectedArticle.value = article
        _currentScrollPosition.value = 0f
    }

    /**
     * 更新滾動位置
     * @param position 新的滾動位置 (0.0 ~ 1.0)
     */
    fun updateScrollPosition(position: Float) {
        _currentScrollPosition.value = position.coerceIn(0f, 1f)
    }

    /**
     * 重新載入文章列表
     */
    fun refreshArticles() {
        loadArticles()
    }

    /**
     * 清除選擇的文章，回到列表
     */
    fun clearSelectedArticle() {
        _selectedArticle.value = null
        _currentScrollPosition.value = 0f
    }
} 