package com.oojohn.vision.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.vision.domain.usecase.BackgroundServiceUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 背景服務控制 ViewModel
 * 管理背景眼動追蹤服務的 UI 狀態和操作
 */
@HiltViewModel
class BackgroundServiceViewModel @Inject constructor(
    private val backgroundServiceUseCase: BackgroundServiceUseCase
) : ViewModel() {

    /**
     * 服務是否正在運行
     */
    val isServiceRunning: StateFlow<Boolean> = backgroundServiceUseCase.isServiceRunning

    /**
     * 覆蓋權限是否已授予
     */
    val overlayPermissionGranted: StateFlow<Boolean> = backgroundServiceUseCase.overlayPermissionGranted

    /**
     * 無障礙服務是否已啟用
     */
    val accessibilityServiceEnabled: StateFlow<Boolean> = backgroundServiceUseCase.accessibilityServiceEnabled

    init {
        checkAllPermissions()
    }

    /**
     * 啟動背景服務
     */
    fun startBackgroundService() {
        viewModelScope.launch {
            if (backgroundServiceUseCase.checkAllPermissions()) {
                backgroundServiceUseCase.startBackgroundService()
            }
        }
    }

    /**
     * 停止背景服務
     */
    fun stopBackgroundService() {
        viewModelScope.launch {
            backgroundServiceUseCase.stopBackgroundService()
        }
    }

    /**
     * 請求覆蓋權限
     */
    fun requestOverlayPermission() {
        viewModelScope.launch {
            backgroundServiceUseCase.requestOverlayPermission()
        }
    }

    /**
     * 開啟無障礙服務設定
     */
    fun openAccessibilitySettings() {
        viewModelScope.launch {
            backgroundServiceUseCase.openAccessibilitySettings()
        }
    }

    /**
     * 檢查所有權限
     */
    fun checkAllPermissions() {
        viewModelScope.launch {
            backgroundServiceUseCase.checkAllPermissions()
        }
    }

    /**
     * 檢查覆蓋權限
     */
    fun checkOverlayPermission() {
        viewModelScope.launch {
            backgroundServiceUseCase.checkOverlayPermission()
        }
    }

    /**
     * 檢查無障礙服務
     */
    fun checkAccessibilityService() {
        viewModelScope.launch {
            backgroundServiceUseCase.checkAccessibilityService()
        }
    }

    /**
     * 更新服務運行狀態
     */
    fun updateServiceRunningState(isRunning: Boolean) {
        viewModelScope.launch {
            backgroundServiceUseCase.updateServiceRunningState(isRunning)
        }
    }
}