package com.oojohn.vision.ui.viewmodel

import android.app.Application
import android.content.Intent
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.vision.domain.model.EyeGazePosition
import com.oojohn.vision.domain.model.UIState
import com.oojohn.vision.domain.usecase.EyeTrackingUseCase
import com.oojohn.vision.service.EyeControlAccessibilityService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 眼動追蹤 ViewModel
 * 負責管理相機狀態、眼動偵測，以及 UI 狀態
 */
@HiltViewModel
class EyeTrackingViewModel @Inject constructor(
    private val eyeTrackingUseCase: EyeTrackingUseCase,
    private val application: Application
) : ViewModel() {

    /**
     * 相機初始化狀態
     */
    val cameraState: StateFlow<UIState<Unit>> = eyeTrackingUseCase.cameraState

    /**
     * 當前眼動位置 - 使用自己的狀態管理
     */
    private val _eyeGazePosition = MutableStateFlow<EyeGazePosition>(EyeGazePosition.Unknown)
    val eyeGazePosition: StateFlow<EyeGazePosition> = _eyeGazePosition.asStateFlow()

    /**
     * 追蹤狀態
     */
    private val _isTracking = MutableStateFlow(false)
    val isTracking: StateFlow<Boolean> = _isTracking.asStateFlow()

    /**
     * 當前生命週期擁有者
     */
    private var currentLifecycleOwner: LifecycleOwner? = null

    init {
        // 監聽 UseCase 的眼動位置更新
        viewModelScope.launch {
            eyeTrackingUseCase.eyeGazePosition.collect { position ->
                android.util.Log.d("EyeTracking", "ViewModel 收到位置更新: $position")
                _eyeGazePosition.value = position
            }
        }
    }

    /**
     * 啟動相機與眼動追蹤
     * @param lifecycleOwner 生命週期擁有者
     */
    fun startEyeTracking(lifecycleOwner: LifecycleOwner) {
        viewModelScope.launch {
            android.util.Log.d("EyeTracking", "開始啟動眼動追蹤，當前狀態: ${_isTracking.value}")

            // 總是先停止當前的追蹤（如果有的話）
            if (_isTracking.value) {
                android.util.Log.d("EyeTracking", "眼動追蹤已啟動，先停止再重新啟動")
                eyeTrackingUseCase.stopTracking()
                _isTracking.value = false
                _eyeGazePosition.value = EyeGazePosition.Unknown
            }

            currentLifecycleOwner = lifecycleOwner
            android.util.Log.d("EyeTracking", "開始重新啟動眼動追蹤")
            val success = eyeTrackingUseCase.startTracking(lifecycleOwner)
            _isTracking.value = success

            android.util.Log.d("EyeTracking", "眼動追蹤啟動結果: $success")

            // 如果成功啟動追蹤，同時啟動背景服務
            if (success) {
                startBackgroundService()
                android.util.Log.d("EyeTracking", "眼動追蹤和背景服務已啟動")
            } else {
                android.util.Log.e("EyeTracking", "眼動追蹤啟動失敗")
            }
        }
    }

    /**
     * 停止相機與眼動追蹤
     */
    fun stopEyeTracking() {
        android.util.Log.d("EyeTracking", "停止眼動追蹤")
        eyeTrackingUseCase.stopTracking()
        _isTracking.value = false
        _eyeGazePosition.value = EyeGazePosition.Unknown
        currentLifecycleOwner = null
        
        // 停止背景服務
        stopBackgroundService()
    }

    /**
     * 切換眼動追蹤狀態
     * @param lifecycleOwner 生命週期擁有者
     */
    fun toggleEyeTracking(lifecycleOwner: LifecycleOwner) {
        android.util.Log.d("EyeTracking", "切換眼動追蹤狀態，當前狀態: ${_isTracking.value}")
        if (_isTracking.value) {
            stopEyeTracking()
        } else {
            startEyeTracking(lifecycleOwner)
        }
    }

    /**
     * 設定眼動追蹤靈敏度
     * @param sensitivity 靈敏度 (0.0 ~ 1.0)
     */
    fun setSensitivity(sensitivity: Float) {
        android.util.Log.d("EyeTracking", "設定靈敏度: $sensitivity")
        eyeTrackingUseCase.setSensitivity(sensitivity)
    }

    /**
     * 檢查是否正在追蹤
     */
    fun isCurrentlyTracking(): Boolean = _isTracking.value

    /**
     * 啟動背景服務
     */
    private fun startBackgroundService() {
        try {
            android.util.Log.d("EyeTracking", "啟動背景服務")
            val intent = Intent(application, EyeControlAccessibilityService::class.java)
            intent.action = EyeControlAccessibilityService.ACTION_START
            application.startService(intent)
        } catch (e: Exception) {
            android.util.Log.e("EyeTracking", "啟動背景服務失敗: ${e.message}")
        }
    }

    /**
     * 停止背景服務
     */
    private fun stopBackgroundService() {
        try {
            android.util.Log.d("EyeTracking", "停止背景服務")
            val intent = Intent(application, EyeControlAccessibilityService::class.java)
            intent.action = EyeControlAccessibilityService.ACTION_STOP
            application.startService(intent)
        } catch (e: Exception) {
            android.util.Log.e("EyeTracking", "停止背景服務失敗: ${e.message}")
        }
    }

    /**
     * ViewModel 清理時停止相機
     */
    override fun onCleared() {
        super.onCleared()
        android.util.Log.d("EyeTracking", "ViewModel 清理")
        stopEyeTracking()
    }
}