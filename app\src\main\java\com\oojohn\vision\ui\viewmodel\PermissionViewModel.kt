package com.oojohn.vision.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.vision.domain.usecase.PermissionUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 權限管理 ViewModel
 * 處理權限相關的 UI 狀態和邏輯
 */
@HiltViewModel
class PermissionViewModel @Inject constructor(
    private val permissionUseCase: PermissionUseCase
) : ViewModel() {

    /**
     * 相機權限是否已授予
     */
    val cameraPermissionGranted: StateFlow<Boolean> = permissionUseCase.cameraPermissionGranted

    /**
     * 權限是否被永久拒絕
     */
    val permissionDeniedPermanently: StateFlow<Boolean> = permissionUseCase.permissionDeniedPermanently

    /**
     * 檢查相機權限
     * @return 權限是否已授予
     */
    fun checkCameraPermission(): Boolean {
        return permissionUseCase.checkCameraPermission()
    }

    /**
     * 更新相機權限狀態
     * @param granted 是否已授予權限
     */
    fun updateCameraPermissionStatus(granted: Boolean) {
        viewModelScope.launch {
            permissionUseCase.updateCameraPermissionStatus(granted)
        }
    }

    /**
     * 設定權限永久拒絕狀態
     * @param denied 是否永久拒絕
     */
    fun setPermissionDeniedPermanently(denied: Boolean) {
        viewModelScope.launch {
            permissionUseCase.setPermissionDeniedPermanently(denied)
        }
    }

    /**
     * 取得所需權限列表
     * @return 權限陣列
     */
    fun getRequiredPermissions(): Array<String> {
        return permissionUseCase.getRequiredPermissions()
    }

    /**
     * 檢查是否所有必要權限都已授予
     * @return 是否已授予所有權限
     */
    fun areAllPermissionsGranted(): Boolean {
        return permissionUseCase.areAllPermissionsGranted()
    }

    /**
     * 重置權限狀態
     */
    fun resetPermissionStates() {
        viewModelScope.launch {
            permissionUseCase.resetPermissionStates()
        }
    }
}