<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/overlay_background"
    android:orientation="vertical"
    android:padding="12dp"
    android:gravity="center">

    <ImageView
        android:id="@+id/gaze_indicator"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_remove"
        android:contentDescription="@string/gaze_position_indicator"
        android:layout_marginBottom="4dp" />

    <TextView
        android:id="@+id/status_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/gaze_position_center"
        android:textSize="10sp"
        android:textColor="@android:color/white"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end" />

</LinearLayout>