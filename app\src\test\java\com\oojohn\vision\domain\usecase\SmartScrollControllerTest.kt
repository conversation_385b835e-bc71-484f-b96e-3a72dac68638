package com.oojohn.vision.domain.usecase

import com.oojohn.vision.domain.model.EyeGazePosition
import com.oojohn.vision.domain.model.ReadingSettings
import com.oojohn.vision.domain.model.SensitivityLevel
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * SmartScrollController 的單元測試
 * 驗證智能滾動邏輯的正確性
 */
class SmartScrollControllerTest {

    private lateinit var smartScrollController: SmartScrollController

    @Before
    fun setUp() {
        smartScrollController = SmartScrollController()
    }

    @Test
    fun `測試靈敏度調整 - 低靈敏度`() {
        val baseDistance = 200
        val result = smartScrollController.adjustScrollDistanceForSensitivity(
            baseDistance, SensitivityLevel.LOW
        )
        assertEquals(140, result) // 200 * 0.7 = 140
    }

    @Test
    fun `測試靈敏度調整 - 中等靈敏度`() {
        val baseDistance = 200
        val result = smartScrollController.adjustScrollDistanceForSensitivity(
            baseDistance, SensitivityLevel.MEDIUM
        )
        assertEquals(200, result) // 保持原值
    }

    @Test
    fun `測試靈敏度調整 - 高靈敏度`() {
        val baseDistance = 200
        val result = smartScrollController.adjustScrollDistanceForSensitivity(
            baseDistance, SensitivityLevel.HIGH
        )
        assertEquals(260, result) // 200 * 1.3 = 260
    }

    @Test
    fun `測試讀取設定預設值`() {
        val settings = ReadingSettings()

        // 驗證預設值
        assertTrue(settings.autoScrollEnabled)
        assertEquals(SensitivityLevel.MEDIUM, settings.sensitivity)
        assertEquals(5, settings.scrollSpeed)
        assertEquals(100, settings.upScrollDistance)
        assertEquals(200, settings.downScrollDistance)
        assertTrue(settings.smartScrollEnabled)
    }

    @Test
    fun `測試自訂讀取設定`() {
        val settings = ReadingSettings(
            upScrollDistance = 80,
            downScrollDistance = 300,
            smartScrollEnabled = false,
            sensitivity = SensitivityLevel.HIGH
        )

        // 驗證自訂值
        assertEquals(80, settings.upScrollDistance)
        assertEquals(300, settings.downScrollDistance)
        assertFalse(settings.smartScrollEnabled)
        assertEquals(SensitivityLevel.HIGH, settings.sensitivity)
    }
}
