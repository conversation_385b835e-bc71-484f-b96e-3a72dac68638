@echo off
echo ========================================
echo 眼動追蹤功能測試腳本
echo ========================================
echo.
echo 此腳本將幫助您測試眼動追蹤功能
echo.
echo 測試步驟:
echo 1. 確保手機已連接並啟用 USB 調試
echo 2. 安裝並啟動應用程式
echo 3. 按照提示進行測試
echo.
echo ========================================

:MENU
echo.
echo 請選擇操作:
echo 1. 檢查設備連接
echo 2. 安裝應用程式
echo 3. 啟動應用程式
echo 4. 查看眼動追蹤日誌
echo 5. 清除應用程式數據
echo 6. 退出
echo.
set /p choice=請輸入選項 (1-6): 

if "%choice%"=="1" goto CHECK_DEVICE
if "%choice%"=="2" goto INSTALL_APP
if "%choice%"=="3" goto LAUNCH_APP
if "%choice%"=="4" goto VIEW_LOGS
if "%choice%"=="5" goto CLEAR_DATA
if "%choice%"=="6" goto EXIT
echo 無效選項，請重新選擇
goto MENU

:CHECK_DEVICE
echo.
echo 檢查設備連接...
adb devices
echo.
pause
goto MENU

:INSTALL_APP
echo.
echo 編譯並安裝應用程式...
call gradlew assembleDebug
if %ERRORLEVEL% EQU 0 (
    adb install -r app\build\outputs\apk\debug\app-debug.apk
    echo 安裝完成
) else (
    echo 編譯失敗
)
echo.
pause
goto MENU

:LAUNCH_APP
echo.
echo 啟動應用程式...
adb shell am start -n com.oojohn.vision/.MainActivity
echo 應用程式已啟動
echo.
pause
goto MENU

:VIEW_LOGS
echo.
echo 開始監控眼動追蹤日誌...
echo 按 Ctrl+C 停止監控
echo.
adb logcat -s EyeTracking -v time
goto MENU

:CLEAR_DATA
echo.
echo 清除應用程式數據...
adb shell pm clear com.oojohn.vision
echo 數據已清除
echo.
pause
goto MENU

:EXIT
echo.
echo 測試結束
pause
exit
